/* Responsive Design */

/* Large Desktop */
@media (min-width: 1400px) {
    .content-area {
        padding: 40px;
    }
    
    .stats-row {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .charts-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop */
@media (max-width: 1200px) {
    :root {
        --sidebar-width: 250px;
    }
    
    .content-area {
        padding: 25px;
    }
    
    .stats-row {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
    
    .action-buttons {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

/* Tablet */
@media (max-width: 992px) and (min-width: 769px) {
    :root {
        --sidebar-width: 70px;
    }

    .sidebar {
        width: 70px;
    }

    .sidebar .logo-text,
    .sidebar .nav-link span {
        display: none;
    }

    .main-content {
        margin-left: 70px;
    }
}
    
    .content-area {
        padding: 20px;
    }
    
    .stats-row {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .charts-row {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .table-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: center;
    }
}

/* Mobile Large */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 280px;
    }

    .sidebar {
        width: var(--sidebar-width);
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 999;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        position: relative;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .header {
        padding: 0 20px;
    }
    
    .page-title {
        font-size: 20px;
    }
    
    .content-area {
        padding: 16px;
    }
    
    .stats-row {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
    }
    
    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .stat-content h3 {
        font-size: 24px;
    }
    
    .dashboard-grid {
        gap: 20px;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .action-btn {
        padding: 14px 16px;
    }
    
    .chart-card .card-body {
        padding: 16px;
    }
    
    .quick-actions {
        padding: 20px;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .data-table {
        min-width: 600px;
    }
    
    .data-table th,
    .data-table td {
        padding: 12px 16px;
        font-size: 14px;
    }
    
    .modal {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px 20px;
    }
    
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }
    
    .toast {
        min-width: auto;
        width: 100%;
    }
    
    .user-menu-dropdown {
        right: -10px;
        left: auto;
        min-width: 160px;
    }
    
    .header-actions {
        gap: 8px;
    }
    
    .theme-toggle,
    .refresh-btn {
        padding: 8px;
        font-size: 16px;
    }
    
    .user-menu-toggle span {
        display: none;
    }
}

/* Mobile Small */
@media (max-width: 480px) {
    :root {
        --sidebar-width: 280px;
    }

    .sidebar {
        width: var(--sidebar-width);
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 999;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        position: relative;
    }

    .header {
        padding: 0 16px;
    }

    .page-title {
        font-size: 18px;
    }

    .content-area {
        padding: 12px;
    }

    .stats-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .stat-card {
        padding: 16px;
        flex-direction: row;
        text-align: left;
        gap: 16px;
    }

    .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    .stat-content h3 {
        font-size: 20px;
    }

    .stat-content p {
        font-size: 13px;
    }

    .dashboard-grid {
        gap: 16px;
    }

    .chart-card .card-header,
    .chart-card .card-body {
        padding: 12px 16px;
    }

    .quick-actions {
        padding: 16px;
    }

    .quick-actions h3 {
        font-size: 16px;
        margin-bottom: 16px;
    }

    .action-btn {
        padding: 12px 14px;
        font-size: 13px;
    }

    .table-header {
        padding: 16px 20px;
    }

    .table-title {
        font-size: 16px;
    }

    .data-table th,
    .data-table td {
        padding: 10px 12px;
        font-size: 13px;
    }

    .btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .btn-sm {
        padding: 6px 10px;
        font-size: 11px;
    }

    .form-control {
        padding: 10px 14px;
        font-size: 13px;
    }

    .modal-header .modal-title {
        font-size: 16px;
    }

    .toast {
        padding: 12px 16px;
    }

    .toast-title {
        font-size: 14px;
    }

    .toast-message {
        font-size: 13px;
    }

    /* Page specific mobile styles */
    .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .page-header-actions {
        justify-content: center;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .operations-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
    }

    .overview-card {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .overview-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .overview-content h3 {
        font-size: 24px;
    }

    .log-filters {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .log-type-tabs {
        flex-direction: column;
    }

    .filter-tabs {
        flex-wrap: wrap;
    }

    .cards-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .users-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .recent-activity {
        padding: 16px;
    }

    .activity-item {
        padding: 10px;
        gap: 10px;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .activity-text {
        font-size: 13px;
    }

    .activity-meta {
        font-size: 11px;
        gap: 8px;
    }
}

/* Mobile Overlay */
@media (max-width: 768px) {
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }
    
    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }
    
    .sidebar {
        z-index: 1000;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header,
    .mobile-menu-toggle,
    .theme-toggle,
    .refresh-btn,
    .user-menu,
    .action-buttons,
    .table-actions,
    .modal-overlay,
    .toast-container {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-area {
        padding: 0 !important;
    }
    
    .page {
        display: block !important;
    }
    
    .chart-card,
    .stat-card,
    .table-container {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }
    
    .stats-row {
        grid-template-columns: repeat(2, 1fr) !important;
    }
    
    .charts-row {
        grid-template-columns: 1fr !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
        --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
        --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.4);
    }
    
    .nav-link:hover,
    .btn:hover {
        outline: 2px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-screen {
        display: none !important;
    }
}
