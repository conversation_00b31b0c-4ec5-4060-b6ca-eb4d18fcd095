<?php
session_start();

// 定义目录
$sourceDir = '假头数据/';
$maleDir = '男/';
$femaleDir = '女/';

// 检查目录是否存在
if (!is_dir($sourceDir) || !is_dir($maleDir) || !is_dir($femaleDir)) {
    die('目录不存在');
}

// 如果会话中没有保存图片文件名，则随机选择一张图片
if (!isset($_SESSION['current_image'])) {
    $images = glob($sourceDir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);

    // 如果没有图片，显示提示信息
    if (empty($images)) {
        die('没有找到图片');
    }

    // 随机选择一张图片并保存到会话中
    $_SESSION['current_image'] = $images[array_rand($images)];
}

// 获取当前展示的图片
$image = $_SESSION['current_image'];

// 如果表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'];
    $imageName = basename($image); // 确认文件名

    if ($action === '男') {
        if (rename($image, $maleDir . $imageName)) {
            // 移动成功后，清除会话中的图片名
            unset($_SESSION['current_image']);
        } else {
            die('移动到男目录失败');
        }
    } elseif ($action === '女') {
        if (rename($image, $femaleDir . $imageName)) {
            // 移动成功后，清除会话中的图片名
            unset($_SESSION['current_image']);
        } else {
            die('移动到女目录失败');
        }
    }

    // 刷新页面
    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机图片展示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        h1 {
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            margin-bottom: 20px;
        }
        .button-container {
            display: flex;
            justify-content: space-between;
            width: 300px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        button:hover {
            opacity: 0.8;
        }
        button:active {
            transform: scale(0.95);
        }
        .male {
            background-color: #4CAF50; /* 绿色 */
            color: white;
        }
        .female {
            background-color: #2196F3; /* 蓝色 */
            color: white;
        }
    </style>
    <script>
        document.addEventListener('keydown', function(event) {
            if (event.key === '1') {
                document.querySelector('button[name="action"][value="男"]').click();
            } else if (event.key === '0') {
                document.querySelector('button[name="action"][value="女"]').click();
            }
        });
    </script>
</head>
<body>
   
    <img src="<?php echo $image; ?>" alt="展示图片">
    <div class="button-container">
        <form method="post">
            <button type="submit" name="action" value="男" class="male">男</button>
        </form>
        <form method="post">
            <button type="submit" name="action" value="女" class="female">女</button>
        </form>
    </div>
</body>
</html>
