# 系统管理后台

一个现代化的企业级系统管理后台，基于您现有的 admapi 接口构建，提供完整的用户管理、卡密管理、系统设置、数据统计和操作日志功能。

## 功能特性

### 🎯 核心功能
- **用户管理** - 查看、封禁/解封、充值会员
- **卡密管理** - 生成、查看、使用状态管理
- **系统设置** - 软件信息配置
- **数据统计** - 启动量、注册量统计与可视化
- **操作日志** - 用户操作记录查看与分析
- **仪表盘** - 系统概览与快速操作

### 🎨 界面特性
- **现代化设计** - 采用最新的 UI 设计趋势
- **响应式布局** - 完美适配桌面、平板、手机
- **深色模式** - 支持明暗主题切换
- **动画效果** - 流畅的交互动画
- **多语言支持** - 中文界面

### 🔧 技术特性
- **原生技术栈** - 纯 HTML5 + CSS3 + JavaScript
- **模块化架构** - 组件化开发，易于维护
- **API 集成** - 完整对接现有 admapi 接口
- **数据可视化** - Chart.js 图表展示
- **本地存储** - 用户偏好设置持久化

## 文件结构

```
admin/
├── index.html              # 主页面
├── login.html              # 登录页面
├── config.php              # 后端配置文件
├── README.md               # 说明文档
├── assets/
│   ├── css/
│   │   ├── style.css       # 主样式文件
│   │   ├── components.css  # 组件样式
│   │   └── responsive.css  # 响应式样式
│   └── js/
│       ├── app.js          # 主应用程序
│       ├── utils.js        # 工具函数
│       ├── api.js          # API 接口层
│       ├── components.js   # UI 组件
│       └── pages/
│           ├── dashboard.js    # 仪表盘页面
│           ├── users.js        # 用户管理页面
│           ├── cards.js        # 卡密管理页面
│           ├── settings.js     # 系统设置页面
│           ├── statistics.js   # 数据统计页面
│           └── logs.js         # 操作日志页面
```

## 安装部署

### 环境要求
- PHP 8.0.2 或更高版本
- Nginx 1.15.11 或更高版本
- MySQL 数据库
- 现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）

### 部署步骤

1. **上传文件**
   ```bash
   # 将 admin 目录上传到您的 Web 服务器
   # 确保与现有的 admapi 目录在同一级别
   ```

2. **配置权限**
   ```bash
   # 设置适当的文件权限
   chmod 755 admin/
   chmod 644 admin/*.html
   chmod 644 admin/assets/css/*.css
   chmod 644 admin/assets/js/*.js
   ```

3. **数据库配置**
   - 确保 `db.php` 文件配置正确
   - 管理后台会自动使用现有的数据库连接

4. **访问后台**
   - 打开浏览器访问：`http://your-domain.com/admin/`
   - 首次访问会跳转到登录页面

5. **API测试**
   - 访问 `http://your-domain.com/admin/test.html` 测试所有API接口
   - 验证新增的统计API是否正常工作

## 使用说明

### 登录系统
1. 访问 `admin/login.html`
2. 输入用户名和密码（演示版本接受任意非空凭据）
3. 点击登录进入管理后台

### 主要功能

#### 仪表盘
- 查看系统关键指标
- 快速操作入口
- 最近活动记录
- 数据可视化图表

#### 用户管理
- 查看所有用户列表
- 用户状态管理（封禁/解封）
- VIP 会员充值
- 用户详细信息查看

#### 卡密管理
- 批量生成卡密
- 查看卡密使用状态
- 卡密使用记录
- 导出卡密数据

#### 系统设置
- 软件版本配置
- 公告信息设置
- 下载链接管理
- 联系方式配置

#### 数据统计
- 用户增长趋势
- 启动量统计
- 数据可视化图表
- 详细统计报表

#### 操作日志
- API 调用日志
- Demo 查询日志
- 用户操作记录
- 日志搜索过滤

### 快捷键
- `Ctrl/Cmd + K` - 聚焦搜索框
- `Ctrl/Cmd + B` - 切换侧边栏
- `Ctrl/Cmd + T` - 切换主题
- `Alt + 1-6` - 快速切换页面
- `Esc` - 关闭弹窗/菜单

## API 接口

管理后台基于您现有的 admapi 接口构建，并新增了以下增强API：

### 原有API接口
- `ban.php` - 用户封禁/解封
- `cz.php` - 用户充值
- `demo.php` - 获取程序数据
- `kamis.php` - 获取卡密列表
- `msghq.php` - 获取系统信息
- `msgxg.php` - 修改系统信息
- `qdltongji.php` - 启动量统计重置
- `sckami.php` - 生成卡密
- `sykami.php` - 使用卡密
- `users.php` - 获取用户列表
- `vipcodes.php` - 更新VIP状态
- `zctongji.php` - 注册量统计重置
- `zhuce.php` - 用户注册

### 新增增强API
- `todaystats.php` - 今日统计查询（启动次数、注册数量、总体统计）
- `cardstats.php` - 卡密详细统计（使用状态、按天数分组、使用率等）
- `userstats.php` - 用户详细统计（VIP、封禁、注册趋势等）
- `logstats.php` - 日志详细分析（API调用、Demo查询、用户活动等）

### API功能说明

#### todaystats.php
获取今日和总体统计数据
```
GET /admapi/todaystats.php
返回：今日启动、注册数量，总用户数、VIP数、卡密统计等
```

#### cardstats.php
卡密状态统计
```
GET /admapi/cardstats.php?status=all|used|unused|stats
- all: 所有卡密
- used: 已使用卡密
- unused: 未使用卡密
- stats: 详细统计信息
```

#### userstats.php
用户详细统计
```
GET /admapi/userstats.php?type=all|vip|banned|normal|stats
- all: 所有用户
- vip: VIP用户列表
- banned: 封禁用户列表
- normal: 普通用户列表
- stats: 详细统计信息
```

#### logstats.php
日志分析统计
```
GET /admapi/logstats.php?date=2025-08-02&type=api|demo|stats
- api: API调用日志
- demo: Demo查询日志
- stats: 详细统计分析
```

## 自定义配置

### 主题定制
在 `assets/css/style.css` 中修改 CSS 变量：
```css
:root {
    --primary-color: #667eea;    /* 主色调 */
    --secondary-color: #764ba2;  /* 辅助色 */
    --accent-color: #f093fb;     /* 强调色 */
    /* ... 更多变量 */
}
```

### 功能扩展
1. 在 `assets/js/pages/` 目录下创建新的页面文件
2. 在 `assets/js/app.js` 中注册新页面
3. 在 `index.html` 中添加导航菜单项

### API 扩展
在 `assets/js/api.js` 中添加新的 API 方法：
```javascript
async newApiMethod(params) {
    return this.request('new-endpoint.php', {
        params: params
    });
}
```

## 浏览器兼容性

| 浏览器 | 版本要求 |
|--------|----------|
| Chrome | 90+ |
| Firefox | 88+ |
| Safari | 14+ |
| Edge | 90+ |

## 性能优化

- 使用 CDN 加载外部资源
- 图片资源压缩优化
- CSS/JS 文件合并压缩
- 启用 Gzip 压缩
- 设置适当的缓存策略

## 安全建议

1. **访问控制**
   - 设置强密码策略
   - 限制管理后台访问 IP
   - 启用 HTTPS

2. **数据保护**
   - 定期备份数据库
   - 敏感信息加密存储
   - 日志文件定期清理

3. **系统维护**
   - 定期更新系统组件
   - 监控系统性能
   - 及时修复安全漏洞

## 技术支持

如有问题或建议，请联系技术支持团队。

## 更新日志

### v1.0.0 (2025-08-02)
- 初始版本发布
- 完整的管理后台功能
- 响应式设计支持
- 深色模式支持

---

© 2025 系统管理后台. 保留所有权利.
