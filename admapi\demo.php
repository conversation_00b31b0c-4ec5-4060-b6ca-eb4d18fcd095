<?php
// 引入数据库配置文件
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

// 获取 token 参数
$token = isset($_GET['token']) ? trim($_GET['token']) : '';

if (empty($token)) {
    echo json_encode(["code" => 400, "message" => "缺少参数 token"], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查用户是否存在
$userQuery = "SELECT * FROM users WHERE token = ?";
$stmt = $mysqli->prepare($userQuery);
$stmt->bind_param("s", $token);
$stmt->execute();
$userResult = $stmt->get_result();

if ($userResult->num_rows === 0) {
    echo json_encode(["code" => 404, "message" => "用户未注册"], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取用户信息
$userInfo = $userResult->fetch_assoc();

// 获取 msg 表的信息
$msgQuery = "SELECT * FROM msg LIMIT 1";
$msgResult = $mysqli->query($msgQuery);

if ($msgResult->num_rows === 0) {
    echo json_encode(["code" => 500, "message" => "获取消息信息失败"], JSON_UNESCAPED_UNICODE);
    exit;
}

$msgInfo = $msgResult->fetch_assoc();

// 更新今日启动次数
$tongjiQuery = "UPDATE tongji SET `今日启动次数` = `今日启动次数` + 1 WHERE 1";
$mysqli->query($tongjiQuery);

// 获取更新后的今日启动次数
$tongjiResult = $mysqli->query("SELECT `今日启动次数` FROM tongji WHERE 1");
$tongjiInfo = $tongjiResult->fetch_assoc();

echo json_encode([
    "code" => 200,
    "message" => "操作成功",
    "user" => $userInfo,
    "msg" => $msgInfo,
    "today_launch_count" => $tongjiInfo['今日启动次数']
], JSON_UNESCAPED_UNICODE);

$stmt->close();
$mysqli->close();
?>
