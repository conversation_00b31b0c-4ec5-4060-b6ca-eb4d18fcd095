<?php
// 引入数据库配置文件
require_once '../db.php';

// 获取当前时间戳
$currentTime = time();
$currentDate = date('Y-m-d', $currentTime);
$yesterdayDate = date('Y-m-d', strtotime('-1 day', $currentTime));

// 开始事务
$mysqli->begin_transaction();

try {
    // 计算昨日注册数量
    $yesterdayQuery = "SELECT COUNT(*) AS count FROM users WHERE DATE(time) = ?";
    $stmt = $mysqli->prepare($yesterdayQuery);
    $stmt->bind_param('s', $yesterdayDate);
    $stmt->execute();
    $stmt->bind_result($yesterdayCount);
    $stmt->fetch();
    $stmt->close();

    // 计算今日注册数量
    $todayQuery = "SELECT COUNT(*) AS count FROM users WHERE DATE(time) = ?";
    $stmt = $mysqli->prepare($todayQuery);
    $stmt->bind_param('s', $currentDate);
    $stmt->execute();
    $stmt->bind_result($todayCount);
    $stmt->fetch();
    $stmt->close();

    // 更新 tongji 表中的数据
    $updateQuery = "UPDATE tongji SET `昨日新用户` = ?, `今日新用户` = ? WHERE 1";
    $stmt = $mysqli->prepare($updateQuery);
    $stmt->bind_param('ii', $yesterdayCount, $todayCount);
    if (!$stmt->execute()) {
        throw new Exception("更新失败: " . $stmt->error);
    }

    // 提交事务
    $mysqli->commit();
    
    echo json_encode(["code" => 200, "message" => "数据更新成功"], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    // 回滚事务
    $mysqli->rollback();
    echo json_encode(["code" => 500, "message" => "操作失败: " . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
