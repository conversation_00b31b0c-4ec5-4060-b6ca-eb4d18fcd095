<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit817ef3b1d71364d47eb6bf1f8b4bd246
{
    public static $prefixLengthsPsr4 = array (
        'J' => 
        array (
            'Jxlwqq\\IdValidator\\' => 19,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Jxlwqq\\IdValidator\\' => 
        array (
            0 => __DIR__ . '/..' . '/jxlwqq/id-validator/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit817ef3b1d71364d47eb6bf1f8b4bd246::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit817ef3b1d71364d47eb6bf1f8b4bd246::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit817ef3b1d71364d47eb6bf1f8b4bd246::$classMap;

        }, null, ClassLoader::class);
    }
}
