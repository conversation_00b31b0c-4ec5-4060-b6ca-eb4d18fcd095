import os
import json
import random
import requests
import datetime
import uuid
import hashlib
from typing import Dict, Any, Op<PERSON>, Tu<PERSON>
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, ContextTypes, CallbackQueryHandler, MessageHandler, filters
from storage import JsonStorage
from config import *

# 创建存储实例
storage = JsonStorage()

# 缓存实现
class QueryCache:
    def __init__(self, cache_dir: str = CACHE_DIR):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def _get_cache_key(self, command: str, params: Dict[str, str]) -> str:
        """生成缓存键"""
        params_str = json.dumps(params, sort_keys=True)
        return hashlib.md5(f"{command}:{params_str}".encode()).hexdigest()
    
    def _get_cache_path(self, cache_key: str) -> str:
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"{cache_key}.json")
    
    def get_cached_response(self, command: str, params: Dict[str, str]) -> Dict[str, Any]:
        """获取缓存的响应"""
        cache_key = self._get_cache_key(command, params)
        cache_path = self._get_cache_path(cache_key)
        
        if os.path.exists(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def save_response(self, command: str, params: Dict[str, str], response: Dict[str, Any]):
        """保存响应到缓存"""
        cache_key = self._get_cache_key(command, params)
        cache_path = self._get_cache_path(cache_key)
        
        with open(cache_path, 'w', encoding='utf-8') as f:
            json.dump(response, f, ensure_ascii=False, indent=2)

class ImageQueryCache(QueryCache):
    def __init__(self, cache_dir: str = CACHE_DIR):
        super().__init__(cache_dir)
        self.image_dir = os.path.join(cache_dir, "images")
        os.makedirs(self.image_dir, exist_ok=True)
    
    def _get_image_path(self, cache_key: str) -> str:
        """获取图片缓存路径"""
        return os.path.join(self.image_dir, f"{cache_key}.png")
    
    def get_cached_image(self, command: str, params: Dict[str, str]) -> str:
        """获取缓存的图片路径"""
        cache_key = self._get_cache_key(command, params)
        image_path = self._get_image_path(cache_key)
        
        return image_path if os.path.exists(image_path) else None
    
    def save_image(self, command: str, params: Dict[str, str], image_data: bytes):
        """保存图片到缓存"""
        cache_key = self._get_cache_key(command, params)
        image_path = self._get_image_path(cache_key)
        
        with open(image_path, 'wb') as f:
            f.write(image_data)

# 创建缓存实例
cache = QueryCache(CACHE_DIR)
image_cache = ImageQueryCache(CACHE_DIR)

# 广播状态存储
broadcast_states = {}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /start 命令"""
    user = update.effective_user
    user_id = user.id
    username = user.username or "未设置用户名"
    nickname = user.first_name or "未设置昵称"

    # 检查是否是新用户
    db_user = storage.get_user(user_id)
    if not db_user:
        # 检查是否有邀请参数
        args = context.args
        inviter_id = int(args[0]) if args and args[0].isdigit() else None
        if inviter_id:
            # 验证邀请人是否存在
            inviter = storage.get_user(inviter_id)
            if not inviter:
                inviter_id = None
        
        # 创建新用户
        storage.create_user(user_id, username, nickname, inviter_id)
        db_user = storage.get_user(user_id)

    # 构建欢迎消息
    vip_status = "VIP会员" if storage.is_vip(user_id) else "普通用户"
    welcome_text = f'''👋 Welcome to MikaxBot

👤 用户信息
━━━━━━━━━━━━━━━━━━━━━━
🆔 用户ID：{user_id}
👤 用户名：@{username}
📝 昵称：{nickname}
💰 积分：{db_user['points']}
🎖 身份：{vip_status}
━━━━━━━━━━━━━━━━━━━━━━

🎯 邀请链接
https://t.me/{context.bot.username}?start={user_id}
📌 邀请奖励：
• 邀请人获得 5 积分
• 被邀请人获得 2 积分

使用 /menu 查看功能菜单'''

    # 创建内联键盘
    keyboard = [
        [
            InlineKeyboardButton("🎯 功能菜单", callback_data="menu"),
            InlineKeyboardButton("💰 我的积分", callback_data="points")
        ],
        [
            InlineKeyboardButton("👥 邀请好友", callback_data="invite")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(welcome_text, reply_markup=reply_markup)

async def menu_command(update: Update, context: ContextTypes.DEFAULT_TYPE, is_callback: bool = False):
    """显示主菜单"""
    user = update.effective_user
    user_data = storage.get_user(user.id)
    
    # 如果用户不存在，创建新用户
    if not user_data:
        storage.create_user(user.id, user.username or '', user.first_name or '')
        user_data = storage.get_user(user.id)
    
    # 添加刷新时间
    current_time = datetime.datetime.now().strftime('%H:%M:%S')
    
    menu_text = f"""
🎉 Welcome to MikaxBot！
👤 用户ID：`{user_data['user_id']}`
💰 当前积分：`{user_data['points']}`
🎫 会员状态：{'✅ VIP' if user_data['is_vip'] else '❌ 普通用户'}

📋 功能菜单 \[{current_time}\]
━━━━━━━━━━━━━━━━━━━━━━
💰 当前积分：`{user_data['points']}`
🎖 用户身份：*{'✅ VIP' if user_data['is_vip'] else '❌ 普通用户'}*
👥 邀请人数：`{storage.get_invite_count(user.id)}`人

📌 请点击下方按钮选择功能
💎 VIP特权：_VIP用户使用功能不消耗积分_
━━━━━━━━━━━━━━━━━━━━━━"""

    # 创建内联键盘
    keyboard = [
        [
            InlineKeyboardButton("👨‍👩‍👧‍👦 文字全户", callback_data="show_qh"),
            InlineKeyboardButton("🎫 白底个户", callback_data="show_gh")
        ],
        [
            InlineKeyboardButton("📁 档案个户", callback_data="show_da"),
            InlineKeyboardButton("📱 三网机主", callback_data="show_jz")
        ],
        [
            InlineKeyboardButton("🚔 刑侦个户", callback_data="show_bg")
        ],
        [
            InlineKeyboardButton("✨ 每日签到", callback_data="checkin"),
            InlineKeyboardButton("👥 邀请好友", callback_data="invite")
        ],
        [
            InlineKeyboardButton("💰 购买积分", callback_data="buy_points"),
            InlineKeyboardButton("💎 开通会员", callback_data="buy_vip")
        ],
        [
            InlineKeyboardButton("💫 刷新菜单", callback_data="menu")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    if is_callback:
        return menu_text, reply_markup
    else:
        await update.message.reply_text(menu_text, reply_markup=reply_markup, parse_mode='MarkdownV2')

async def handle_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理内联键盘回调"""
    query = update.callback_query
    user_id = query.from_user.id
    
    try:
        await query.answer()

        # 功能展示按钮的回调处理
        if query.data.startswith("show_"):
            command = query.data[5:]  # 获取命令名称
            points = POINTS_CONFIG[command]
            
            # 根据不同命令设置不同的说明文本和示例链接
            command_info = {
                'qh': ('文字全户查询', '姓名 身份证', 'https://t.me/riverfbl/12'),
                'gh': ('白底个户查询', '姓名 身份证', 'https://t.me/riverfbl/13'),
                'da': ('档案个户查询', '姓名 身份证', 'https://t.me/riverfbl/14'),
                'jz': ('三网机主查询', '手机号', 'https://t.me/riverfbl/15'),
                'bg': ('刑侦个户查询', '姓名 身份证', 'https://t.me/riverfbl/16')
            }
            
            info = command_info[command]
            command_text = f'''*💫 {info[0]}*
━━━━━━━━━━━━━━━━━━━━━━
📝 使用方法：`/{command} {info[1]}`
💰 消耗积分：`{points}`分
⚡️ VIP特权：_免费使用_

*📌 注意事项：*
• 请严格按照格式输入
• 确保输入信息准确
• 注意隐私安全
━━━━━━━━━━━━━━━━━━━━━━'''

            # 创建带有示例按钮的键盘
            keyboard = [
                [
                    InlineKeyboardButton("👀 查看示例", url=info[2]),
                    InlineKeyboardButton("🔙 返回菜单", callback_data="menu")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            try:
                await query.edit_message_text(command_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            except Exception as e:
                await query.message.reply_text(command_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            return

        if query.data == "buy_points":
            points_text = f'''💰 *购买积分*
━━━━━━━━━━━━━━━━━━━━━━
📌 充值方案：
• 30元 \\= 35积分
• 60元 \\= 70积分
• 100元 \\= 150积分

💳 支付方式：
• 支付宝
• 微信支付
• USDT

👨‍💻 请联系客服购买：
@Riverkefu\\_bot
━━━━━━━━━━━━━━━━━━━━━━'''

            keyboard = [
                [InlineKeyboardButton("👨‍💻 联系客服", url="https://t.me/Riverkefu_bot")],
                [InlineKeyboardButton("🔙 返回菜单", callback_data="menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            try:
                await query.edit_message_text(points_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            except Exception as e:
                await query.message.reply_text(points_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            return

        elif query.data == "buy_vip":
            vip_text = f'''💎 *开通会员*
━━━━━━━━━━━━━━━━━━━━━━
📌 会员价格：
• 月卡 \\= 360元
• 年卡 \\= 800元
• 永久 \\= 1800元

💎 会员特权：
• 所有功能免费使用
• 无限次数查询
• 优先处理请求
• 专属客服服务

💳 支付方式：
• 支付宝
• 微信支付
• USDT

👨‍💻 请联系客服开通：
@Riverkefu\\_bot
━━━━━━━━━━━━━━━━━━━━━━'''

            keyboard = [
                [InlineKeyboardButton("👨‍💻 联系客服", url="https://t.me/Riverkefu_bot")],
                [InlineKeyboardButton("🔙 返回菜单", callback_data="menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            try:
                await query.edit_message_text(vip_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            except Exception as e:
                await query.message.reply_text(vip_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            return

        elif query.data == "points":
            user = storage.get_user(user_id)
            points_text = f'''💰 *积分查询*
━━━━━━━━━━━━━━━━━━━━━━
当前积分：`{user['points']}`

💡 温馨提示：
• 开通会员免积分使用
• 购买积分更优惠
• 邀请好友得奖励
━━━━━━━━━━━━━━━━━━━━━━'''

            keyboard = [
                [InlineKeyboardButton("💰 购买积分", callback_data="buy_points"),
                 InlineKeyboardButton("💎 开通会员", callback_data="buy_vip")],
                [InlineKeyboardButton("👨‍💻 联系客服", url="https://t.me/Riverkefu_bot")],
                [InlineKeyboardButton("🔙 返回菜单", callback_data="menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            try:
                await query.edit_message_text(points_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            except Exception as e:
                await query.message.reply_text(points_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
            return

        # 在积分不足的情况下添加充值引导
        if query.data.startswith("show_"):
            command = query.data[5:]
            points = POINTS_CONFIG[command]
            
            if not storage.is_vip(user_id) and not storage.check_points(user_id, points):
                not_enough_points_text = f'''❌ *积分不足*
━━━━━━━━━━━━━━━━━━━━━━
💰 当前积分：`{storage.get_user(user_id)["points"]}`
📍 需要积分：`{points}`
🎯 差额积分：`{points - storage.get_user(user_id)["points"]}`

💡 获取积分方式：
• 购买积分
• 开通会员
• 邀请好友
• 每日签到

👨‍💻 联系客服充值：
@Riverkefu\\_bot
━━━━━━━━━━━━━━━━━━━━━━'''

                keyboard = [
                    [InlineKeyboardButton("💰 购买积分", callback_data="buy_points"),
                     InlineKeyboardButton("💎 开通会员", callback_data="buy_vip")],
                    [InlineKeyboardButton("👨‍💻 联系客服", url="https://t.me/Riverkefu_bot")],
                    [InlineKeyboardButton("🔙 返回菜单", callback_data="menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.message.edit_text(not_enough_points_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
                return

        if query.data == "checkin":
            invite_count = storage.get_invite_count(user_id)
            last_checkin = storage.get_last_checkin(user_id)
            
            # 检查是否已经签到
            if last_checkin:
                last_checkin_date = datetime.datetime.strptime(last_checkin, '%Y-%m-%d %H:%M:%S').date()
                today = datetime.datetime.now().date()
                if last_checkin_date == today:
                    await query.message.edit_text("❌ 您今天已经签到过了，明天再来吧！")
                    return

            # 检查邀请人数
            if invite_count < 10:
                not_enough_invites_text = f'''❌ *邀请人数不足*
━━━━━━━━━━━━━━━━━━━━━━
👥 当前邀请：`{invite_count}`人
📍 需要邀请：`10`人
🎯 还差人数：`{10 - invite_count}`人

💡 温馨提示：
• 开通会员免邀请签到
• 购买积分更便捷
• 邀请好友得奖励

👨‍💻 联系客服咨询：
@Riverkefu\\_bot
━━━━━━━━━━━━━━━━━━━━━━'''

                keyboard = [
                    [InlineKeyboardButton("💰 购买积分", callback_data="buy_points"),
                     InlineKeyboardButton("💎 开通会员", callback_data="buy_vip")],
                    [InlineKeyboardButton("👨‍💻 联系客服", url="https://t.me/Riverkefu_bot")],
                    [InlineKeyboardButton("🔙 返回菜单", callback_data="menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.message.edit_text(not_enough_invites_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
                return
            
            # 执行签到
            storage.update_checkin(user_id)
            storage.update_user_points(user_id, 5, '每日签到')
            
            checkin_text = f'''*✨ 签到成功*
━━━━━━━━━━━━━━━━━━━━━━
🎯 获得积分：\+5
💰 当前积分：`{storage.get_user(user_id)["points"]}`
👥 邀请人数：`{invite_count}`人
━━━━━━━━━━━━━━━━━━━━━━'''
            
            await query.message.edit_text(checkin_text, parse_mode='MarkdownV2')
            return

        # 其他按钮的回调处理
        if query.data == "menu":
            menu_text, reply_markup = await menu_command(update, context, is_callback=True)
            await query.message.edit_text(menu_text, reply_markup=reply_markup, parse_mode='MarkdownV2')
        elif query.data == "invite":
            invite_link = f"https://t.me/{context.bot.username}?start={query.from_user.id}"
            invite_text = f'''*🎯 邀请好友*
━━━━━━━━━━━━━━━━━━━━━━
*📌 您的邀请链接：*
`{invite_link}`

*💰 邀请奖励：*
• 邀请人获得 *5* 积分
• 被邀请人获得 *2* 积分
━━━━━━━━━━━━━━━━━━━━━━'''
            await query.message.edit_text(invite_text, parse_mode='MarkdownV2')

    except Exception as e:
        await query.message.reply_text(f'发生错误：{str(e)}')

async def invite_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /invite 命令"""
    user_id = update.effective_user.id
    user = storage.get_user(user_id)
    if not user:
        await update.message.reply_text("❌ 请先使用 /start 初始化您的账号")
        return

    invite_link = f"https://t.me/{context.bot.username}?start={user_id}"
    
    text = f'''🎯 邀请好友
━━━━━━━━━━━━━━━━━━━━━━
📌 您的邀请链接：
{invite_link}

💰 邀请奖励：
- 邀请人获得5积分
- 被邀请人获得2积分

📊 邀请统计：
累计邀请：{len([u for u in storage.get_all_users() if u['inviter_id'] == user_id])}人
━━━━━━━━━━━━━━━━━━━━━━'''

    # 创建分享按钮
    keyboard = [[InlineKeyboardButton("📲 分享给好友", url=invite_link)]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(text, reply_markup=reply_markup)

# 修改原有的命令处理函数，添加积分检查
async def check_points_and_vip(user_id: int, command: str) -> Tuple[bool, str]:
    """检查用户积分和VIP状态"""
    if storage.is_vip(user_id):
        return True, ""
    
    points_needed = POINTS_CONFIG[command]
    if not storage.check_points(user_id, points_needed):
        return False, f"❌ 积分不足\n需要{points_needed}积分，当前积分：{storage.get_user(user_id)['points']}"
    
    return True, ""

# 修改原有的命令处理函数
async def qh_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    can_proceed, message = await check_points_and_vip(user_id, 'qh')
    if not can_proceed:
        await update.message.reply_text(message)
        return

    # 如果不是VIP，扣除积分
    if not storage.is_vip(user_id):
        storage.update_user_points(user_id, -POINTS_CONFIG['qh'], '使用全户查询')

    # 原有的处理逻辑
    try:
        # 获取命令参数
        args = context.args
        if len(args) != 2:
            await update.message.reply_text('请使用正确的格式：/qh 姓名 身份证')
            return

        name, idcard = args
        params = {'name': name, 'idcard': idcard}
        
        # 尝试从缓存获取
        cached_response = cache.get_cached_response('qh', params)
        if cached_response:
            data = cached_response
        else:
            # 调用family.php API
            response = requests.get(FAMILY_API, params=params)
            data = response.json()
            if data.get('code') == 200:  # 只缓存成功的响应
                cache.save_response('qh', params, data)

        if data.get('code') != 200:
            await update.message.reply_text(f'查询失败：{data.get("message", "未知错误")}')
            return

        # 构建文字全户树状结构
        family_data = data.get('data', [])
        if not family_data:
            await update.message.reply_text('未找到文字全户信息')
            return

        # 生成查询编号和时间
        query_id = str(uuid.uuid4())[:8].upper()
        query_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 构建树状结构
        tree_text = f'''👨‍👩‍👧‍👦 文字全户调查结果
━━━━━━━━━━━━━━━━━━━━━━
🔍 查询编号：{query_id}
⏰ 查询时间：{query_time}
🎯 查询目标：{name}
━━━━━━━━━━━━━━━━━━━━━━
👥 文字全户信息：
'''
        for i, member in enumerate(family_data):
            prefix = '├─➤ ' if i < len(family_data) - 1 else '└─➤ '
            tree_text += f'{prefix}{member["name"]} [{member["idcard"]}]\n'

        tree_text += '\n📌 共计成员：{} 人\n━━━━━━━━━━━━━━━━━━━━━━'.format(len(family_data))

        await update.message.reply_text(tree_text)

    except Exception as e:
        await update.message.reply_text(f'发生错误：{str(e)}')

async def gh_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    can_proceed, message = await check_points_and_vip(user_id, 'gh')
    if not can_proceed:
        await update.message.reply_text(message)
        return

    # 如果不是VIP，扣除积分
    if not storage.is_vip(user_id):
        storage.update_user_points(user_id, -POINTS_CONFIG['gh'], '使用三网机主查询')

    try:
        # 获取命令参数
        args = context.args
        if len(args) != 2:
            await update.message.reply_text('请使用正确的格式：/gh 姓名 身份证')
            return

        name, idcard = args
        params = {'xm': name, 'hm': idcard, 'token': GH_TOKEN}

        # 尝试从缓存获取响应数据
        cached_response = cache.get_cached_response('gh', params)
        cached_image_path = image_cache.get_cached_image('gh', params)

        if cached_response and cached_image_path:
            data = cached_response
            temp_path = cached_image_path
        else:
            # 调用个户查询API
            response = requests.get(GH_API, params=params)
            data = response.json()

            if data.get('code') != '200':
                await update.message.reply_text(f'查询失败：{data.get("message", "未知错误")}')
                return

            # 获取图片URL并下载
            img_url = data.get('imgurl')
            if not img_url:
                await update.message.reply_text('查询失败：未获取到图片信息')
                return

            # 下载图片
            img_response = requests.get(img_url)
            if img_response.status_code != 200:
                await update.message.reply_text('查询失败：图片下载失败')
                return

            # 保存到缓存
            if data.get('code') == '200':
                cache.save_response('gh', params, data)
                image_cache.save_image('gh', params, img_response.content)
                temp_path = image_cache.get_cached_image('gh', params)

        # 生成查询编号和时间
        query_id = str(uuid.uuid4())[:8].upper()
        query_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 发送图片和说明文字
        caption = f'''🎫 白底个户调查结果
━━━━━━━━━━━━━━━━━━━━━━
🔍 查询编号：{query_id}
⏰ 查询时间：{query_time}
🎯 查询目标：{name}

⚠️ 数据来源：China-{random.randint(1, 50)}-线路公安数据库
📌 查询结果：请查看附件图片
━━━━━━━━━━━━━━━━━━━━━━'''

        # 发送图片消息
        with open(temp_path, 'rb') as photo:
            await update.message.reply_photo(photo=photo, caption=caption)

    except Exception as e:
        await update.message.reply_text(f'发生错误：{str(e)}')

async def da_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    can_proceed, message = await check_points_and_vip(user_id, 'da')
    if not can_proceed:
        await update.message.reply_text(message)
        return

    # 如果不是VIP，扣除积分
    if not storage.is_vip(user_id):
        storage.update_user_points(user_id, -POINTS_CONFIG['da'], '使用档案个户查询')

    try:
        # 获取命令参数
        args = context.args
        if len(args) != 2:
            await update.message.reply_text('请使用正确的格式：/da 姓名 身份证')
            return

        name, idcard = args
        params = {'xm': name, 'hm': idcard, 'token': DA_TOKEN}

        # 尝试从缓存获取响应数据
        cached_response = cache.get_cached_response('da', params)
        cached_image_path = image_cache.get_cached_image('da', params)

        if cached_response and cached_image_path:
            data = cached_response
            temp_path = cached_image_path
        else:
            # 调用档案查询API
            response = requests.get(DA_API, params=params)
            data = response.json()

            if data.get('code') != '200':
                await update.message.reply_text(f'查询失败：{data.get("message", "未知错误")}')
                return

            # 获取图片URL并下载
            img_url = data.get('imgurl')
            if not img_url:
                await update.message.reply_text('查询失败：未获取到图片信息')
                return

            # 下载图片
            img_response = requests.get(img_url)
            if img_response.status_code != 200:
                await update.message.reply_text('查询失败：图片下载失败')
                return

            # 保存到缓存
            if data.get('code') == '200':
                cache.save_response('da', params, data)
                image_cache.save_image('da', params, img_response.content)
                temp_path = image_cache.get_cached_image('da', params)

        # 生成查询编号和时间
        query_id = str(uuid.uuid4())[:8].upper()
        query_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 发送图片和说明文字
        caption = f'''📁 档案个户调查结果
━━━━━━━━━━━━━━━━━━━━━━
🔍 查询编号：{query_id}
⏰ 查询时间：{query_time}
🎯 查询目标：{name}

⚠️ 数据来源：China-{random.randint(1, 50)}-线路公安数据库
📌 查询结果：请查看附件图片
━━━━━━━━━━━━━━━━━━━━━━'''

        # 发送图片消息
        with open(temp_path, 'rb') as photo:
            await update.message.reply_photo(photo=photo, caption=caption)

    except Exception as e:
        await update.message.reply_text(f'发生错误：{str(e)}')

async def jz_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    can_proceed, message = await check_points_and_vip(user_id, 'jz')
    if not can_proceed:
        await update.message.reply_text(message)
        return

    # 如果不是VIP，扣除积分
    if not storage.is_vip(user_id):
        storage.update_user_points(user_id, -POINTS_CONFIG['jz'], '使用三网机主查询')

    try:
        # 获取命令参数
        args = context.args
        if len(args) != 1:
            await update.message.reply_text('请使用正确的格式：/jz 手机号')
            return

        phone = args[0]

        # 验证手机号格式
        if not phone.isdigit() or len(phone) != 11:
            await update.message.reply_text('手机号格式不正确')
            return

        params = {'phone': phone}
        
        # 尝试从缓存获取
        cached_response = cache.get_cached_response('jz', params)
        if cached_response:
            data = cached_response
        else:
            # 调用手机号API
            response = requests.get(PHONE_API, params=params)
            data = response.json()
            if data.get('code') == 200:  # 只缓存成功的响应
                cache.save_response('jz', params, data)

        if data.get('code') != 200:
            await update.message.reply_text(f'查询失败：{data.get("message", "未知错误")}')
            return

        # 格式化返回信息
        result_data = data.get('data', {})
        location = result_data.get('location', {})
        
        # 生成查询编号和时间
        query_id = str(uuid.uuid4())[:8].upper()
        query_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        response_text = f'''📡 三网机主调查结果
━━━━━━━━━━━━━━━━━━━━━━
🔍 查询编号：{query_id}
⏰ 查询时间：{query_time}

📋 基本信息
┣━━ 手机号码：{phone}
┣━━ 归属地区：{location.get("province")} {location.get("city")}
┗━━ 运营商：{location.get("sp")}

👤 用户信息
┣━━ 姓名：{result_data.get("name")}
┣━━ 身份证：{result_data.get("idcard")}
┗━━ 地址：{result_data.get("address")}
━━━━━━━━━━━━━━━━━━━━━━'''

        await update.message.reply_text(response_text)

    except Exception as e:
        await update.message.reply_text(f'发生错误：{str(e)}')

async def bg_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    can_proceed, message = await check_points_and_vip(user_id, 'bg')
    if not can_proceed:
        await update.message.reply_text(message)
        return

    # 如果不是VIP，扣除积分
    if not storage.is_vip(user_id):
        storage.update_user_points(user_id, -POINTS_CONFIG['bg'], '使用刑侦个户查询')

    try:
        # 获取命令参数
        args = context.args
        if len(args) != 2:
            await update.message.reply_text('请使用正确的格式：/bg 姓名 身份证')
            return

        name, idcard = args
        params = {'name': name, 'idcard': idcard, 'key': 'admin912'}

        # 尝试从缓存获取响应数据
        cached_response = cache.get_cached_response('bg', params)
        cached_image_path = image_cache.get_cached_image('bg', params)

        if cached_response and cached_image_path:
            data = cached_response
            temp_path = cached_image_path
        else:
            # 调用报告查询API
            response = requests.get(BG_API, params=params)
            data = response.json()

            if data.get('code') != 200:
                await update.message.reply_text(f'查询失败：{data.get("message", "未知错误")}')
                return

            # 获取返回数据
            result_data = data.get('data', {})
            if not result_data:
                await update.message.reply_text('查询失败：未获取到有效数据')
                return
                
            # 获取图片URL
            img_url = result_data.get('image_url')
            if not img_url:
                await update.message.reply_text('查询失败：未获取到图片信息')
                return

            # 下载图片
            img_response = requests.get(img_url)
            if img_response.status_code != 200:
                await update.message.reply_text('查询失败：图片下载失败')
                return

            # 保存到缓存
            if data.get('code') == 200:
                cache.save_response('bg', params, data)
                image_cache.save_image('bg', params, img_response.content)
                temp_path = image_cache.get_cached_image('bg', params)

        # 获取返回数据
        result_data = data.get('data', {})

        # 生成查询编号和时间
        query_id = str(uuid.uuid4())[:8].upper()
        query_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 发送图片和说明文字
        caption = f'''🚔 刑侦个户调查结果
━━━━━━━━━━━━━━━━━━━━━━
🔍 查询编号：{query_id}
⏰ 查询时间：{query_time}

👤 用户基本信息
┣━━ 姓名：{result_data.get("name")}
┣━━ 身份证：{result_data.get("idcard")}
┣━━ 详细地址：{result_data.get("fake_address")}
┗━━ 三级地址：{result_data.get("real_address")}

⚠️ 数据来源：China-{random.randint(1, 50)}-线路公安数据库
📌 查询结果：请查看附件图片
━━━━━━━━━━━━━━━━━━━━━━'''

        # 发送图片消息
        with open(temp_path, 'rb') as photo:
            await update.message.reply_photo(photo=photo, caption=caption)

    except Exception as e:
        await update.message.reply_text(f'发生错误：{str(e)}')

async def broadcast_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /ojbk 命令"""
    user_id = update.effective_user.id
    
    # 检查是否是管理员
    if not storage.is_admin(user_id):
        await update.message.reply_text("❌ 抱歉，您没有权限使用此命令。")
        return
    
    # 设置广播状态
    broadcast_states[user_id] = {
        'status': 'waiting_content',
        'content': None,
        'user_count': 0
    }
    
    await update.message.reply_text(
        "📢 广播系统已启动\n"
        "请发送您要广播的内容："
    )

async def handle_broadcast_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理广播内容"""
    user_id = update.effective_user.id
    
    # 检查是否在广播状态
    if user_id not in broadcast_states:
        return
    
    state = broadcast_states[user_id]
    
    if state['status'] == 'waiting_content':
        # 保存广播内容
        state['content'] = update.message
        state['user_count'] = len(storage.get_all_users())
        
        # 创建确认键盘
        keyboard = [
            [
                InlineKeyboardButton("✅ 开始广播", callback_data="broadcast_start"),
                InlineKeyboardButton("❌ 取消广播", callback_data="broadcast_cancel")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # 显示广播预览
        preview_text = (
            f"📢 广播预览\n"
            f"━━━━━━━━━━━━━━━━━━━━━━\n"
            f"📝 内容：\n{update.message.text}\n"
            f"👥 目标用户数：{state['user_count']}\n"
            f"━━━━━━━━━━━━━━━━━━━━━━\n"
            f"是否开始广播？"
        )
        
        await update.message.reply_text(preview_text, reply_markup=reply_markup)
        state['status'] = 'waiting_confirmation'

async def handle_broadcast_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理广播回调"""
    query = update.callback_query
    user_id = query.from_user.id
    
    if user_id not in broadcast_states:
        await query.answer("广播会话已过期", show_alert=True)
        return
    
    state = broadcast_states[user_id]
    
    if query.data == "broadcast_start":
        await query.edit_message_text("📢 开始广播...")
        
        # 获取所有用户
        users = storage.get_all_users()
        success_count = 0
        fail_count = 0
        
        # 发送广播
        for user in users:
            try:
                await context.bot.forward_message(
                    chat_id=user['user_id'],
                    from_chat_id=query.message.chat_id,
                    message_id=state['content'].message_id
                )
                success_count += 1
            except Exception as e:
                fail_count += 1
        
        # 发送广播结果
        result_text = (
            f"📢 广播完成\n"
            f"━━━━━━━━━━━━━━━━━━━━━━\n"
            f"✅ 成功：{success_count}\n"
            f"❌ 失败：{fail_count}\n"
            f"━━━━━━━━━━━━━━━━━━━━━━"
        )
        
        await query.edit_message_text(result_text)
        
    elif query.data == "broadcast_cancel":
        await query.edit_message_text("❌ 广播已取消")
    
    # 清理广播状态
    del broadcast_states[user_id]

def main():
    # 创建Application实例
    application = Application.builder().token(TOKEN).build()

    # 注册命令处理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("menu", menu_command))
    application.add_handler(CommandHandler("invite", invite_command))
    application.add_handler(CommandHandler("qh", qh_command))
    application.add_handler(CommandHandler("gh", gh_command))
    application.add_handler(CommandHandler("da", da_command))
    application.add_handler(CommandHandler("jz", jz_command))
    application.add_handler(CommandHandler("bg", bg_command))
    application.add_handler(CommandHandler("ojbk", broadcast_command))
    
    # 注册回调查询处理器
    # 先注册广播回调处理器，确保它能优先处理广播相关的回调
    application.add_handler(CallbackQueryHandler(handle_broadcast_callback, pattern="^broadcast_"))
    # 再注册其他消息处理器
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_broadcast_message))
    # 最后注册通用回调处理器
    application.add_handler(CallbackQueryHandler(handle_callback))

    # 启动机器人
    application.run_polling()

if __name__ == '__main__':
    main()