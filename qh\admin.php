<?php
session_start();

// 配置信息
$admin_username = 'admin';
$admin_password = 'your_password_here'; // 请修改为安全的密码
$data_dir = 'data';

// 检查登录状态
function check_login() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// 处理登录
if (isset($_POST['login'])) {
    if ($_POST['username'] === $admin_username && $_POST['password'] === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
        header('Location: admin.php');
        exit;
    } else {
        $error = '用户名或密码错误';
    }
}

// 处理退出登录
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// 读取JSON文件
function read_json($file) {
    if (file_exists($file)) {
        return json_decode(file_get_contents($file), true);
    }
    return [];
}

// 保存JSON文件
function save_json($file, $data) {
    file_put_contents($file, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
}

// 处理用户操作
if (check_login()) {
    $users_file = $data_dir . '/users.json';
    $users = read_json($users_file);

    // 处理积分操作
    if (isset($_POST['add_points'])) {
        $user_id = $_POST['user_id'];
        $points = intval($_POST['points']);
        if (isset($users[$user_id])) {
            $users[$user_id]['points'] += $points;
            save_json($users_file, $users);
        }
    }

    // 处理VIP操作
    if (isset($_POST['set_vip'])) {
        $user_id = $_POST['user_id'];
        $days = intval($_POST['days']);
        if (isset($users[$user_id])) {
            $current_time = new DateTime();
            if ($users[$user_id]['is_vip'] && $users[$user_id]['vip_expire_time']) {
                $current_time = new DateTime($users[$user_id]['vip_expire_time']);
            }
            $current_time->modify("+$days days");
            $users[$user_id]['is_vip'] = true;
            $users[$user_id]['vip_expire_time'] = $current_time->format('Y-m-d H:i:s');
            save_json($users_file, $users);
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>机器人管理后台</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <?php if (!check_login()): ?>
            <!-- 登录表单 -->
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-center">管理员登录</h3>
                        </div>
                        <div class="card-body">
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger"><?php echo $error; ?></div>
                            <?php endif; ?>
                            <form method="post">
                                <div class="mb-3">
                                    <label class="form-label">用户名</label>
                                    <input type="text" name="username" class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">密码</label>
                                    <input type="password" name="password" class="form-control" required>
                                </div>
                                <button type="submit" name="login" class="btn btn-primary w-100">登录</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- 管理界面 -->
            <div class="row mb-3">
                <div class="col">
                    <h2>用户管理</h2>
                </div>
                <div class="col text-end">
                    <a href="?logout=1" class="btn btn-danger">退出登录</a>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>用户名</th>
                            <th>昵称</th>
                            <th>积分</th>
                            <th>VIP状态</th>
                            <th>VIP到期时间</th>
                            <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user_id => $user): ?>
                            <tr>
                                <td><?php echo $user['user_id']; ?></td>
                                <td><?php echo $user['username']; ?></td>
                                <td><?php echo $user['nickname']; ?></td>
                                <td><?php echo $user['points']; ?></td>
                                <td><?php echo $user['is_vip'] ? '是' : '否'; ?></td>
                                <td><?php echo $user['vip_expire_time'] ?: '-'; ?></td>
                                <td><?php echo $user['created_at']; ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#pointsModal<?php echo $user_id; ?>">
                                        修改积分
                                    </button>
                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#vipModal<?php echo $user_id; ?>">
                                        设置VIP
                                    </button>
                                </td>
                            </tr>

                            <!-- 积分修改模态框 -->
                            <div class="modal fade" id="pointsModal<?php echo $user_id; ?>">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">修改积分</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form method="post">
                                            <div class="modal-body">
                                                <input type="hidden" name="user_id" value="<?php echo $user_id; ?>">
                                                <div class="mb-3">
                                                    <label class="form-label">积分变动（正数增加，负数减少）</label>
                                                    <input type="number" name="points" class="form-control" required>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <button type="submit" name="add_points" class="btn btn-primary">确定</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- VIP设置模态框 -->
                            <div class="modal fade" id="vipModal<?php echo $user_id; ?>">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">设置VIP</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form method="post">
                                            <div class="modal-body">
                                                <input type="hidden" name="user_id" value="<?php echo $user_id; ?>">
                                                <div class="mb-3">
                                                    <label class="form-label">VIP天数</label>
                                                    <input type="number" name="days" class="form-control" required>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <button type="submit" name="set_vip" class="btn btn-primary">确定</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</body>
</html> 