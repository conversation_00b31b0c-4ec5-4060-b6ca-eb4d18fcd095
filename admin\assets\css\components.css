/* Dashboard Components */
.dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.stat-content h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stat-content p {
    color: var(--text-muted);
    font-size: 14px;
    font-weight: 500;
}

/* Charts */
.charts-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.card-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    padding: 24px;
}

/* Quick Actions */
.quick-actions {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.quick-actions h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.action-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 16px 20px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.action-btn i {
    font-size: 16px;
}

/* Tables */
.table-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.table-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.table-actions {
    display: flex;
    gap: 12px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 16px 24px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table td {
    color: var(--text-secondary);
}

.data-table tbody tr:hover {
    background: var(--bg-secondary);
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.btn-secondary:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-lg {
    padding: 14px 28px;
    font-size: 16px;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(72, 187, 120, 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgba(245, 101, 101, 0.1);
    color: var(--error-color);
}

.status-pending {
    background: rgba(237, 137, 54, 0.1);
    color: var(--warning-color);
}

.status-vip {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #b45309;
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.toast {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px 20px;
    box-shadow: var(--shadow-heavy);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    transform: translateX(100%);
    transition: var(--transition);
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--error-color);
}

.toast-warning {
    border-left: 4px solid var(--warning-color);
}

.toast-info {
    border-left: 4px solid var(--info-color);
}

.toast-icon {
    font-size: 18px;
}

.toast-success .toast-icon {
    color: var(--success-color);
}

.toast-error .toast-icon {
    color: var(--error-color);
}

.toast-warning .toast-icon {
    color: var(--warning-color);
}

.toast-info .toast-icon {
    color: var(--info-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.toast-message {
    color: var(--text-secondary);
    font-size: 14px;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.toast-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Page Specific Styles */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.page-header-content h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.page-header-content p {
    color: var(--text-muted);
    font-size: 16px;
}

.page-header-actions {
    display: flex;
    gap: 12px;
}

/* Users Page Styles */
.users-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.user-token {
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--primary-color);
}

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 120px;
}

.detail-value {
    flex: 1;
    text-align: right;
    color: var(--text-secondary);
}

/* Generated Cards Styles */
.generated-cards-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    background: var(--bg-secondary);
}

.generated-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: var(--bg-primary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.generated-card:last-child {
    margin-bottom: 0;
}

.card-code {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: var(--primary-color);
    font-weight: 600;
}

.copy-card {
    padding: 4px 8px;
    font-size: 12px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10002;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    background: var(--bg-primary);
    padding: 40px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-heavy);
    min-width: 200px;
}

.loading-content .spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 20px;
}

.loading-content p {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 500;
}

/* Search Box */
.search-box {
    position: relative;
    display: inline-block;
}

.search-box .form-control {
    padding-right: 40px;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

/* Pagination */
.table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    min-width: 60px;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-pages {
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0 8px;
}

/* Text Utilities */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--error-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

/* Margin Utilities */
.mt-3 {
    margin-top: 1rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.ml-3 {
    margin-left: 1rem !important;
}

.mr-3 {
    margin-right: 1rem !important;
}

.ml-2 {
    margin-left: 0.5rem !important;
}

/* Cards Page Styles */
.cards-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.cards-filters {
    margin-bottom: 30px;
}

.filter-tabs {
    display: flex;
    gap: 8px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0;
}

.filter-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    position: relative;
}

.filter-tab:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.filter-tab.active {
    background: var(--primary-color);
    color: white;
}

.filter-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

.card-code-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-code {
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--primary-color);
    font-weight: 600;
}

.card-days {
    background: linear-gradient(135deg, var(--info-color), var(--primary-color));
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.copy-card-btn {
    opacity: 0.7;
    transition: var(--transition);
}

.copy-card-btn:hover {
    opacity: 1;
    transform: scale(1.1);
}

.card-details {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-text {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 4px;
}

/* Settings Page Styles */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.settings-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.settings-card .card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.settings-card .card-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-card .card-header h3 i {
    color: var(--primary-color);
}

.settings-card .card-body {
    padding: 24px;
}

.system-operations {
    width: 100%;
}

.operations-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.operations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.operation-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
    min-height: 120px;
    justify-content: center;
}

.operation-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.operation-btn i {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.operation-btn span {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.operation-btn small {
    color: var(--text-muted);
    font-size: 12px;
    line-height: 1.3;
}

/* Form Validation */
.form-control.is-invalid {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
}

/* Select Styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
    appearance: none;
}

/* Textarea Styling */
textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

textarea.form-control:focus {
    resize: vertical;
}

/* Statistics Page Styles */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.overview-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.overview-content {
    flex: 1;
}

.overview-content h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.overview-content p {
    color: var(--text-muted);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.overview-change {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

.overview-change.positive {
    background: rgba(72, 187, 120, 0.1);
    color: var(--success-color);
}

.overview-change.negative {
    background: rgba(245, 101, 101, 0.1);
    color: var(--error-color);
}

.charts-section {
    margin-bottom: 30px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.chart-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.chart-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-controls select {
    min-width: 120px;
    padding: 6px 12px;
    font-size: 14px;
}

.chart-body {
    padding: 24px;
    height: 300px;
    position: relative;
}

.detailed-stats {
    width: 100%;
}

.stats-table-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.stats-table-container .table-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.stats-table-container .table-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.stats-table {
    overflow-x: auto;
}

.activity-bar {
    position: relative;
    background: var(--bg-tertiary);
    border-radius: 10px;
    height: 20px;
    min-width: 80px;
    overflow: hidden;
}

.activity-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--success-color), var(--info-color));
    border-radius: 10px;
    transition: width 0.3s ease;
}

.activity-bar span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Logs Page Styles */
.logs-controls {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.log-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.log-type-tabs {
    display: flex;
    gap: 8px;
}

.log-type-tab {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 10px 16px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--text-secondary);
}

.log-type-tab:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.log-type-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.logs-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.log-time {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--text-muted);
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 4px;
}

.api-name {
    background: linear-gradient(135deg, var(--info-color), var(--primary-color));
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 2px;
}

.api-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.api-description {
    color: var(--text-muted);
    font-size: 11px;
    font-style: italic;
}

.ip-address {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: 4px;
}

.id-card {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--warning-color);
    background: rgba(237, 137, 54, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.query-type {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.params-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.param-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.param-item strong {
    color: var(--text-primary);
    min-width: 80px;
    font-size: 14px;
}

.param-item span {
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    word-break: break-all;
}

.demo-result {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.result-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.result-item strong {
    color: var(--text-primary);
    font-size: 14px;
}

.status-code {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 12px;
}

.status-code {
    background: var(--success-color);
    color: white;
}

.result-data {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--text-secondary);
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 300px;
    overflow-y: auto;
}

/* Recent Activity Styles */
.recent-activity {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    grid-column: 1 / -1;
}

.recent-activity h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.recent-activity h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    transition: var(--transition);
}

.activity-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
}

.activity-item[data-type="demo"] {
    border-left-color: var(--success-color);
}

.activity-item[data-type="api"] {
    border-left-color: var(--info-color);
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-text {
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 4px;
}

.activity-text strong {
    color: var(--primary-color);
    font-weight: 600;
}

.activity-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: var(--text-muted);
}

.activity-time {
    font-weight: 500;
}

.activity-ip {
    font-family: 'Courier New', monospace;
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 4px;
}

.activity-uses {
    background: var(--warning-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
}
