<?php
// 引入数据库配置文件
require '../db.php';

// 获取用户列表
$query = "SELECT token, time, vipcode, viptime, tokencode FROM users";
$result = $mysqli->query($query);

// 检查是否有用户数据
if ($result->num_rows > 0) {
    $users = [];

    // 遍历所有用户并添加到用户列表中
    while ($row = $result->fetch_assoc()) {
        $users[] = [
            "token" => $row["token"],
            "time" => $row["time"],
            "vipcode" => $row["vipcode"],
            "viptime" => $row["viptime"],
            "tokencode" => $row["tokencode"]
        ];
    }

    // 返回用户列表
    echo json_encode(["code" => 200, "message" => "获取用户列表成功", "users" => $users], JSON_UNESCAPED_UNICODE);
} else {
    // 没有用户数据
    echo json_encode(["code" => 404, "message" => "没有找到任何用户", "users" => null], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
