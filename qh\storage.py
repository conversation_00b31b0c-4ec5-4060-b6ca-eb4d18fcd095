import json
import os
import time
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

class JsonStorage:
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.users_file = os.path.join(data_dir, "users.json")
        self.cdkeys_file = os.path.join(data_dir, "cdkeys.json")
        self.lock = threading.Lock()
        
        # 创建数据目录
        os.makedirs(data_dir, exist_ok=True)
        
        # 初始化数据文件
        self._init_data_files()
    
    def _init_data_files(self):
        """初始化数据文件"""
        if not os.path.exists(self.users_file):
            self._save_json(self.users_file, {})
        if not os.path.exists(self.cdkeys_file):
            self._save_json(self.cdkeys_file, {})
    
    def _load_json(self, file_path: str) -> dict:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def _save_json(self, file_path: str, data: dict):
        """保存JSON文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def get_user(self, user_id: int) -> Optional[dict]:
        """获取用户信息"""
        with self.lock:
            users = self._load_json(self.users_file)
            return users.get(str(user_id))
    
    def create_user(self, user_id: int, username: str, nickname: str, inviter_id: Optional[int] = None) -> bool:
        """创建新用户"""
        with self.lock:
            users = self._load_json(self.users_file)
            if str(user_id) in users:
                return False
            
            # 创建新用户数据
            users[str(user_id)] = {
                'user_id': user_id,
                'username': username,
                'nickname': nickname,
                'points': 0,
                'is_admin': False,
                'is_vip': False,
                'vip_expire_time': None,
                'inviter_id': inviter_id,
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'last_checkin': None
            }
            
            # 处理邀请奖励
            if inviter_id:
                inviter_str_id = str(inviter_id)
                if inviter_str_id in users:
                    users[inviter_str_id]['points'] += 5  # 邀请人奖励
                    users[str(user_id)]['points'] += 2    # 被邀请人奖励
            
            self._save_json(self.users_file, users)
            return True
    
    def is_vip(self, user_id: int) -> bool:
        """检查用户是否是VIP"""
        user = self.get_user(user_id)
        if not user:
            return False
        
        if not user['is_vip']:
            return False
        
        if not user['vip_expire_time']:
            return False
        
        expire_time = datetime.strptime(user['vip_expire_time'], '%Y-%m-%d %H:%M:%S')
        return expire_time > datetime.now()
    
    def is_admin(self, user_id: int) -> bool:
        """检查用户是否是管理员"""
        user = self.get_user(user_id)
        return user and user['is_admin']
    
    def update_user_points(self, user_id: int, points: int, action: str) -> bool:
        """更新用户积分"""
        with self.lock:
            users = self._load_json(self.users_file)
            user_str_id = str(user_id)
            
            if user_str_id not in users:
                return False
            
            users[user_str_id]['points'] += points
            self._save_json(self.users_file, users)
            return True
    
    def check_points(self, user_id: int, points: int) -> bool:
        """检查用户积分是否足够"""
        user = self.get_user(user_id)
        return user and user['points'] >= points
    
    def create_cdkeys(self, points: int, count: int) -> List[str]:
        """生成卡密"""
        import uuid
        with self.lock:
            cdkeys = self._load_json(self.cdkeys_file)
            codes = []
            
            for _ in range(count):
                code = str(uuid.uuid4()).replace('-', '')[:12].upper()
                cdkeys[code] = {
                    'points': points,
                    'is_used': False,
                    'used_by': None,
                    'used_at': None,
                    'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'type': 'points'
                }
                codes.append(code)
            
            self._save_json(self.cdkeys_file, cdkeys)
            return codes
    
    def use_cdkey(self, code: str, user_id: int) -> tuple[bool, int, str]:
        """使用卡密"""
        with self.lock:
            cdkeys = self._load_json(self.cdkeys_file)
            users = self._load_json(self.users_file)
            
            if code not in cdkeys:
                return False, 0, "卡密不存在"
            
            cdkey = cdkeys[code]
            if cdkey['is_used']:
                return False, 0, "卡密已被使用"
            
            # 更新卡密状态
            cdkey['is_used'] = True
            cdkey['used_by'] = user_id
            cdkey['used_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 更新用户积分
            if cdkey['type'] == 'points':
                users[str(user_id)]['points'] += cdkey['points']
                points = cdkey['points']
            elif cdkey['type'] == 'vip':
                current_time = datetime.now()
                if users[str(user_id)]['is_vip'] and users[str(user_id)]['vip_expire_time']:
                    current_time = datetime.strptime(users[str(user_id)]['vip_expire_time'], '%Y-%m-%d %H:%M:%S')
                
                expire_time = current_time + timedelta(days=cdkey['value'])
                users[str(user_id)]['is_vip'] = True
                users[str(user_id)]['vip_expire_time'] = expire_time.strftime('%Y-%m-%d %H:%M:%S')
                points = 0
            
            self._save_json(self.cdkeys_file, cdkeys)
            self._save_json(self.users_file, users)
            return True, points, "卡密使用成功"
    
    def get_invite_count(self, user_id: int) -> int:
        """获取用户邀请人数"""
        users = self._load_json(self.users_file)
        return sum(1 for user in users.values() if user['inviter_id'] == user_id)
    
    def get_last_checkin(self, user_id: int) -> Optional[str]:
        """获取用户最后签到时间"""
        user = self.get_user(user_id)
        return user['last_checkin'] if user else None
    
    def update_checkin(self, user_id: int):
        """更新用户签到时间"""
        with self.lock:
            users = self._load_json(self.users_file)
            users[str(user_id)]['last_checkin'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self._save_json(self.users_file, users)
    
    def get_all_users(self) -> List[dict]:
        """获取所有用户"""
        users = self._load_json(self.users_file)
        return list(users.values())
    
    def create_vip_cdkey(self, days: int) -> str:
        """生成VIP卡密"""
        import random
        import string
        with self.lock:
            cdkeys = self._load_json(self.cdkeys_file)
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))
            
            cdkeys[code] = {
                'type': 'vip',
                'value': days,
                'is_used': False,
                'used_by': None,
                'used_at': None,
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._save_json(self.cdkeys_file, cdkeys)
            return code
    
    def save_user(self, user_id, username, nickname, inviter_id=None):
        """Save user information to storage."""
        with self.lock:
            user_data = {
                'user_id': user_id,
                'username': username,
                'nickname': nickname,
                'inviter_id': inviter_id,
                'points': 0,
                'is_vip': False,
                'vip_expire_time': None,
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            self._save_json(self.users_file, {str(user_id): user_data})
            return user_data 