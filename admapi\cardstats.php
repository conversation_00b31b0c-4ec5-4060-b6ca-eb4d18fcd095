<?php
/**
 * 卡密统计API
 */

require_once '../db.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $status = $_GET['status'] ?? 'all';
    
    if (!isset($mysqli) || !$mysqli) {
        throw new Exception('数据库连接失败');
    }
    
    $response = [
        'code' => 200,
        'message' => 'success'
    ];
    
    switch ($status) {
        case 'used':
            $result = $mysqli->query("SELECT kami, kamitime, kamicode, oktoken, oktime FROM kamis WHERE kamicode = '1' ORDER BY oktime DESC");
            $cards = [];
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $cards[] = $row;
                }
            }
            $response['kamis'] = $cards;
            break;
            
        case 'unused':
            $result = $mysqli->query("SELECT kami, kamitime, kamicode, oktoken, oktime FROM kamis WHERE kamicode = '0' ORDER BY kami ASC");
            $cards = [];
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $cards[] = $row;
                }
            }
            $response['kamis'] = $cards;
            break;
            
        case 'stats':
            $stats = [];
            
            // 总卡密数
            $result = $mysqli->query("SELECT COUNT(*) as count FROM kamis");
            if ($result) {
                $row = $result->fetch_assoc();
                $stats['total'] = intval($row['count']);
            } else {
                $stats['total'] = 0;
            }
            
            // 已使用卡密数
            $result = $mysqli->query("SELECT COUNT(*) as count FROM kamis WHERE kamicode = '1'");
            if ($result) {
                $row = $result->fetch_assoc();
                $stats['used'] = intval($row['count']);
            } else {
                $stats['used'] = 0;
            }
            
            $stats['unused'] = $stats['total'] - $stats['used'];
            $stats['usage_rate'] = $stats['total'] > 0 ? round(($stats['used'] / $stats['total']) * 100, 2) : 0;
            
            // 最近使用的卡密
            $result = $mysqli->query("SELECT kami, kamitime, oktoken, oktime FROM kamis WHERE kamicode = '1' AND oktime IS NOT NULL ORDER BY oktime DESC LIMIT 10");
            $recentUsed = [];
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $recentUsed[] = $row;
                }
            }
            $stats['recent_used'] = $recentUsed;
            
            $response['stats'] = $stats;
            break;
            
        default:
            $result = $mysqli->query("SELECT kami, kamitime, kamicode, oktoken, oktime FROM kamis ORDER BY kamicode ASC, kami ASC");
            $cards = [];
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $cards[] = $row;
                }
            }
            $response['kamis'] = $cards;
            break;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'code' => 500,
        'message' => '获取卡密数据失败: ' . $e->getMessage()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>
