{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "e564a764a48b01a92f4bed4117c41f3a", "packages": [{"name": "jxlwqq/id-validator", "version": "1.4.33", "source": {"type": "git", "url": "https://github.com/jxlwqq/id-validator.git", "reference": "2884acaebc6149aeeb4444a90afad8614135f577"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jxlwqq/id-validator/zipball/2884acaebc6149aeeb4444a90afad8614135f577", "reference": "2884acaebc6149aeeb4444a90afad8614135f577", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": ">=5.6"}, "type": "library", "autoload": {"psr-4": {"Jxlwqq\\IdValidator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jxlwqq", "email": "<EMAIL>", "homepage": "https://jxlwqq.github.io"}], "description": "Chinese Mainland Personal ID Card Validation", "support": {"issues": "https://github.com/jxlwqq/id-validator/issues", "source": "https://github.com/jxlwqq/id-validator/tree/1.4.33"}, "time": "2021-12-10T06:39:36+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.0.0"}