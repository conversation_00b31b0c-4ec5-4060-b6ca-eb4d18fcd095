// UI Components

// Toast Notification System
class Toast {
    constructor() {
        this.container = utils.$('#toast-container');
        if (!this.container) {
            this.container = utils.createElement('div', {
                id: 'toast-container',
                className: 'toast-container'
            });
            document.body.appendChild(this.container);
        }
    }

    show(message, type = 'info', duration = 5000) {
        const toast = utils.createElement('div', {
            className: `toast toast-${type}`
        });

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <i class="toast-icon ${iconMap[type]}"></i>
            <div class="toast-content">
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        this.container.appendChild(toast);

        // Show animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove
        const autoRemove = setTimeout(() => this.remove(toast), duration);

        // Manual close
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            clearTimeout(autoRemove);
            this.remove(toast);
        });

        return toast;
    }

    remove(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// Modal System
class Modal {
    constructor() {
        this.container = utils.$('#modal-container');
        if (!this.container) {
            this.container = utils.createElement('div', {
                id: 'modal-container'
            });
            document.body.appendChild(this.container);
        }
    }

    show(options = {}) {
        const {
            title = '提示',
            content = '',
            size = 'medium',
            showClose = true,
            buttons = [],
            onShow = null,
            onHide = null
        } = options;

        const overlay = utils.createElement('div', {
            className: 'modal-overlay'
        });

        const modal = utils.createElement('div', {
            className: `modal modal-${size}`
        });

        const header = utils.createElement('div', {
            className: 'modal-header'
        });

        header.innerHTML = `
            <h3 class="modal-title">${title}</h3>
            ${showClose ? '<button class="modal-close"><i class="fas fa-times"></i></button>' : ''}
        `;

        const body = utils.createElement('div', {
            className: 'modal-body',
            innerHTML: content
        });

        const footer = utils.createElement('div', {
            className: 'modal-footer'
        });

        // Add buttons
        buttons.forEach(button => {
            const btn = utils.createElement('button', {
                className: `btn ${button.className || 'btn-secondary'}`,
                textContent: button.text
            });

            btn.addEventListener('click', (e) => {
                if (button.onClick) {
                    const result = button.onClick(e, this);
                    if (result !== false) {
                        this.hide(overlay);
                    }
                } else {
                    this.hide(overlay);
                }
            });

            footer.appendChild(btn);
        });

        modal.appendChild(header);
        modal.appendChild(body);
        if (buttons.length > 0) {
            modal.appendChild(footer);
        }

        overlay.appendChild(modal);
        this.container.appendChild(overlay);

        // Event listeners
        if (showClose) {
            const closeBtn = header.querySelector('.modal-close');
            closeBtn.addEventListener('click', () => this.hide(overlay));
        }

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.hide(overlay);
            }
        });

        // Show modal
        setTimeout(() => overlay.classList.add('active'), 10);

        if (onShow) onShow(modal);

        return {
            modal,
            overlay,
            hide: () => this.hide(overlay)
        };
    }

    hide(overlay) {
        overlay.classList.remove('active');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }

    confirm(message, title = '确认') {
        return new Promise((resolve) => {
            this.show({
                title,
                content: `<p>${message}</p>`,
                buttons: [
                    {
                        text: '取消',
                        className: 'btn-secondary',
                        onClick: () => resolve(false)
                    },
                    {
                        text: '确认',
                        className: 'btn-primary',
                        onClick: () => resolve(true)
                    }
                ]
            });
        });
    }

    alert(message, title = '提示') {
        return new Promise((resolve) => {
            this.show({
                title,
                content: `<p>${message}</p>`,
                buttons: [
                    {
                        text: '确定',
                        className: 'btn-primary',
                        onClick: () => resolve(true)
                    }
                ]
            });
        });
    }

    prompt(message, defaultValue = '', title = '输入') {
        return new Promise((resolve) => {
            const inputId = 'modal-prompt-input';
            this.show({
                title,
                content: `
                    <p>${message}</p>
                    <div class="form-group">
                        <input type="text" id="${inputId}" class="form-control" value="${defaultValue}" placeholder="请输入...">
                    </div>
                `,
                buttons: [
                    {
                        text: '取消',
                        className: 'btn-secondary',
                        onClick: () => resolve(null)
                    },
                    {
                        text: '确定',
                        className: 'btn-primary',
                        onClick: () => {
                            const input = utils.$(`#${inputId}`);
                            resolve(input.value);
                        }
                    }
                ],
                onShow: (modal) => {
                    const input = modal.querySelector(`#${inputId}`);
                    input.focus();
                    input.select();
                }
            });
        });
    }
}

// Loading Overlay
class Loading {
    constructor() {
        this.overlay = null;
    }

    show(message = '加载中...') {
        if (this.overlay) return;

        this.overlay = utils.createElement('div', {
            className: 'loading-overlay',
            innerHTML: `
                <div class="loading-content">
                    <div class="spinner"></div>
                    <p>${message}</p>
                </div>
            `
        });

        document.body.appendChild(this.overlay);
        setTimeout(() => this.overlay.classList.add('active'), 10);
    }

    hide() {
        if (!this.overlay) return;

        this.overlay.classList.remove('active');
        setTimeout(() => {
            if (this.overlay && this.overlay.parentNode) {
                this.overlay.parentNode.removeChild(this.overlay);
                this.overlay = null;
            }
        }, 300);
    }

    update(message) {
        if (this.overlay) {
            const p = this.overlay.querySelector('p');
            if (p) p.textContent = message;
        }
    }
}

// Data Table Component
class DataTable {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? utils.$(container) : container;
        this.options = {
            columns: [],
            data: [],
            pagination: true,
            pageSize: 10,
            searchable: true,
            sortable: true,
            actions: [],
            ...options
        };
        
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.searchTerm = '';
        
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        const { columns, searchable, actions } = this.options;
        
        this.container.innerHTML = `
            <div class="table-container">
                <div class="table-header">
                    <h3 class="table-title">${this.options.title || '数据表格'}</h3>
                    <div class="table-actions">
                        ${searchable ? `
                            <div class="search-box">
                                <input type="text" class="form-control search-input" placeholder="搜索...">
                                <i class="fas fa-search"></i>
                            </div>
                        ` : ''}
                        ${actions.map(action => `
                            <button class="btn ${action.className || 'btn-primary'}" data-action="${action.name}">
                                ${action.icon ? `<i class="${action.icon}"></i>` : ''}
                                ${action.text}
                            </button>
                        `).join('')}
                    </div>
                </div>
                <div class="table-body">
                    <table class="data-table">
                        <thead>
                            <tr>
                                ${columns.map(col => `
                                    <th ${this.options.sortable && col.sortable !== false ? 'class="sortable" data-column="' + col.key + '"' : ''}>
                                        ${col.title}
                                        ${this.options.sortable && col.sortable !== false ? '<i class="fas fa-sort"></i>' : ''}
                                    </th>
                                `).join('')}
                            </tr>
                        </thead>
                        <tbody class="table-data">
                        </tbody>
                    </table>
                </div>
                ${this.options.pagination ? `
                    <div class="table-pagination">
                        <div class="pagination-info">
                            <span class="pagination-text"></span>
                        </div>
                        <div class="pagination-controls">
                            <button class="btn btn-sm pagination-btn" data-action="first">首页</button>
                            <button class="btn btn-sm pagination-btn" data-action="prev">上一页</button>
                            <span class="pagination-pages"></span>
                            <button class="btn btn-sm pagination-btn" data-action="next">下一页</button>
                            <button class="btn btn-sm pagination-btn" data-action="last">末页</button>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

        this.updateTable();
    }

    bindEvents() {
        const container = this.container;

        // Search
        if (this.options.searchable) {
            const searchInput = container.querySelector('.search-input');
            searchInput.addEventListener('input', utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.currentPage = 1;
                this.updateTable();
            }, 300));
        }

        // Sort
        if (this.options.sortable) {
            utils.delegate(container, '.sortable', 'click', (e) => {
                const column = e.target.closest('th').dataset.column;
                if (this.sortColumn === column) {
                    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    this.sortColumn = column;
                    this.sortDirection = 'asc';
                }
                this.updateTable();
            });
        }

        // Pagination
        if (this.options.pagination) {
            utils.delegate(container, '.pagination-btn', 'click', (e) => {
                const action = e.target.dataset.action;
                const totalPages = Math.ceil(this.getFilteredData().length / this.options.pageSize);

                switch (action) {
                    case 'first':
                        this.currentPage = 1;
                        break;
                    case 'prev':
                        this.currentPage = Math.max(1, this.currentPage - 1);
                        break;
                    case 'next':
                        this.currentPage = Math.min(totalPages, this.currentPage + 1);
                        break;
                    case 'last':
                        this.currentPage = totalPages;
                        break;
                }
                this.updateTable();
            });
        }

        // Actions
        utils.delegate(container, '[data-action]', 'click', (e) => {
            const actionName = e.target.dataset.action;
            const action = this.options.actions.find(a => a.name === actionName);
            if (action && action.onClick) {
                action.onClick(e, this);
            }
        });
    }

    getFilteredData() {
        let data = [...this.options.data];

        // Apply search filter
        if (this.searchTerm) {
            data = data.filter(row => {
                return this.options.columns.some(col => {
                    const value = row[col.key];
                    return value && value.toString().toLowerCase().includes(this.searchTerm.toLowerCase());
                });
            });
        }

        // Apply sort
        if (this.sortColumn) {
            data.sort((a, b) => {
                const aVal = a[this.sortColumn];
                const bVal = b[this.sortColumn];
                
                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }

        return data;
    }

    updateTable() {
        const filteredData = this.getFilteredData();
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.options.pagination ? filteredData.slice(startIndex, endIndex) : filteredData;

        // Update table body
        const tbody = this.container.querySelector('.table-data');
        tbody.innerHTML = pageData.map(row => `
            <tr>
                ${this.options.columns.map(col => `
                    <td>
                        ${col.render ? col.render(row[col.key], row) : (row[col.key] || '')}
                    </td>
                `).join('')}
            </tr>
        `).join('');

        // Update sort indicators
        this.container.querySelectorAll('.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        if (this.sortColumn) {
            const sortHeader = this.container.querySelector(`[data-column="${this.sortColumn}"] i`);
            if (sortHeader) {
                sortHeader.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'}`;
            }
        }

        // Update pagination
        if (this.options.pagination) {
            this.updatePagination(filteredData.length);
        }
    }

    updatePagination(totalItems) {
        const totalPages = Math.ceil(totalItems / this.options.pageSize);
        const startItem = (this.currentPage - 1) * this.options.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.options.pageSize, totalItems);

        // Update pagination info
        const paginationText = this.container.querySelector('.pagination-text');
        paginationText.textContent = `显示 ${startItem}-${endItem} 条，共 ${totalItems} 条`;

        // Update pagination buttons
        const firstBtn = this.container.querySelector('[data-action="first"]');
        const prevBtn = this.container.querySelector('[data-action="prev"]');
        const nextBtn = this.container.querySelector('[data-action="next"]');
        const lastBtn = this.container.querySelector('[data-action="last"]');

        firstBtn.disabled = prevBtn.disabled = this.currentPage === 1;
        nextBtn.disabled = lastBtn.disabled = this.currentPage === totalPages;
    }

    setData(data) {
        this.options.data = data;
        this.currentPage = 1;
        this.updateTable();
    }

    refresh() {
        this.updateTable();
    }
}

// Initialize global components
window.toast = new Toast();
window.modal = new Modal();
window.loading = new Loading();
window.DataTable = DataTable;
