<?php
// 引入数据库配置文件
require '../db.php';

// 获取当前时间戳
$current_time = date('Y-m-d H:i:s');

// 更新用户表，将已到期的用户设置为非会员状态，不清除viptime
$stmt = $mysqli->prepare("
    UPDATE users 
    SET vipcode = '0' 
    WHERE vipcode = '1' AND viptime <= ?
");
$stmt->bind_param("s", $current_time);
$stmt->execute();

// 检查受影响的行数
$affected_rows = $stmt->affected_rows;

if ($affected_rows > 0) {
    echo json_encode([
        "code" => 200,
        "message" => "已更新到期的VIP用户",
        "updated_users" => $affected_rows
    ], JSON_UNESCAPED_UNICODE);
} else {
    echo json_encode([
        "code" => 200,
        "message" => "没有需要更新的用户"
    ], JSON_UNESCAPED_UNICODE);
}

$stmt->close();
$mysqli->close();
?>
