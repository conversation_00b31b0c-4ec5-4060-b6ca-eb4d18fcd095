// Dashboard Page Logic

class Dashboard {
    constructor() {
        this.charts = {};
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDashboardData();
        this.startAutoRefresh();
    }

    bindEvents() {
        // Quick action buttons
        utils.delegate(document, '.action-btn', 'click', (e) => {
            const action = e.target.dataset.action;
            this.handleQuickAction(action);
        });

        // Refresh button
        const refreshBtn = utils.$('#refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }
    }

    async loadDashboardData() {
        try {
            loading.show('加载仪表盘数据...');

            const stats = await api.getDashboardStats();
            this.updateStatCards(stats);
            this.initCharts(stats);

            // Load recent activity
            await this.showRecentActivity();

            loading.hide();
        } catch (error) {
            console.error('加载仪表盘数据失败:', error);
            toast.error('加载仪表盘数据失败: ' + error.message);
            loading.hide();
        }
    }

    updateStatCards(stats) {
        const {
            totalUsers = 0,
            vipUsers = 0,
            todayLaunches = 0,
            todayRegisters = 0,
            totalLaunches = 0,
            totalCards = 0,
            usedCards = 0,
            unusedCards = 0
        } = stats;

        // Update stat cards with animation
        this.animateNumber('#total-users', totalUsers);
        this.animateNumber('#vip-users', vipUsers);
        this.animateNumber('#today-launches', todayLaunches);
        this.animateNumber('#today-registers', todayRegisters);

        // Update additional stats if elements exist
        const totalLaunchesEl = utils.$('#total-launches');
        if (totalLaunchesEl) {
            this.animateNumber('#total-launches', totalLaunches);
        }

        const totalCardsEl = utils.$('#total-cards');
        if (totalCardsEl) {
            this.animateNumber('#total-cards', totalCards);
        }
    }

    animateNumber(selector, targetValue, duration = 1000) {
        const element = utils.$(selector);
        if (!element) return;

        const startValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - startValue) / (duration / 16);
        let currentValue = startValue;

        const animate = () => {
            currentValue += increment;
            
            if ((increment > 0 && currentValue >= targetValue) || 
                (increment < 0 && currentValue <= targetValue)) {
                element.textContent = utils.formatNumber(targetValue);
                return;
            }
            
            element.textContent = utils.formatNumber(Math.floor(currentValue));
            requestAnimationFrame(animate);
        };

        animate();
    }

    initCharts(stats) {
        this.initUserGrowthChart(stats);
        this.initLaunchStatsChart(stats);
    }

    initUserGrowthChart(stats) {
        const ctx = utils.$('#userGrowthChart');
        if (!ctx) return;

        // Use real data from stats
        const dailyRegisters = stats.dailyRegisters || [];
        const labels = [];
        const userData = [];

        // Fill in the last 7 days
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            const dayData = dailyRegisters.find(d => d.date === dateStr);

            labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
            userData.push(dayData ? parseInt(dayData.count) : 0);
        }

        if (this.charts.userGrowth) {
            this.charts.userGrowth.destroy();
        }

        this.charts.userGrowth = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '新增用户',
                        data: userData,
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    initLaunchStatsChart(stats) {
        const ctx = utils.$('#launchStatsChart');
        if (!ctx) return;

        const {
            todayLaunches = 0,
            todayRegisters = 0,
            totalCards = 0,
            usedCards = 0,
            unusedCards = 0
        } = stats;

        if (this.charts.launchStats) {
            this.charts.launchStats.destroy();
        }

        this.charts.launchStats = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['已使用卡密', '未使用卡密'],
                datasets: [{
                    data: [usedCards, unusedCards],
                    backgroundColor: [
                        'rgb(72, 187, 120)',
                        'rgb(237, 137, 54)'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    handleQuickAction(action) {
        switch (action) {
            case 'generate-cards':
                this.showGenerateCardsModal();
                break;
            case 'user-management':
                app.switchPage('users');
                break;
            case 'system-settings':
                app.switchPage('settings');
                break;
            case 'view-logs':
                app.switchPage('logs');
                break;
            default:
                console.log('Unknown action:', action);
        }
    }

    async showGenerateCardsModal() {
        const modalContent = `
            <div class="form-group">
                <label class="form-label">卡密天数</label>
                <input type="number" id="card-days" class="form-control" placeholder="请输入天数" min="1" value="30">
            </div>
            <div class="form-group">
                <label class="form-label">生成数量</label>
                <input type="number" id="card-count" class="form-control" placeholder="请输入数量" min="1" max="100" value="10">
            </div>
        `;

        const modalInstance = modal.show({
            title: '生成卡密',
            content: modalContent,
            buttons: [
                {
                    text: '取消',
                    className: 'btn-secondary'
                },
                {
                    text: '生成',
                    className: 'btn-primary',
                    onClick: async () => {
                        const days = utils.$('#card-days').value;
                        const count = utils.$('#card-count').value;

                        if (!days || !count) {
                            toast.error('请填写完整信息');
                            return false;
                        }

                        if (days < 1 || count < 1 || count > 100) {
                            toast.error('参数范围错误');
                            return false;
                        }

                        try {
                            loading.show('正在生成卡密...');
                            const result = await api.generateCards(days, count);
                            loading.hide();

                            if (result.code === 200) {
                                toast.success(`成功生成 ${result.kamis.length} 张卡密`);
                                // Show generated cards
                                this.showGeneratedCards(result.kamis, days);
                            } else {
                                toast.error(result.message || '生成失败');
                            }
                        } catch (error) {
                            loading.hide();
                            toast.error('生成卡密失败: ' + error.message);
                        }
                    }
                }
            ]
        });
    }

    showGeneratedCards(cards, days) {
        const cardsList = cards.map(card => `
            <div class="generated-card">
                <span class="card-code">${card}</span>
                <button class="btn btn-sm btn-secondary copy-card" data-card="${card}">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        `).join('');

        const modalContent = `
            <p>成功生成 ${cards.length} 张 ${days} 天的卡密：</p>
            <div class="generated-cards-list">
                ${cardsList}
            </div>
            <div class="mt-3">
                <button class="btn btn-primary" id="copy-all-cards">
                    <i class="fas fa-copy"></i>
                    复制全部
                </button>
                <button class="btn btn-secondary" id="export-cards">
                    <i class="fas fa-download"></i>
                    导出文件
                </button>
            </div>
        `;

        const modalInstance = modal.show({
            title: '生成的卡密',
            content: modalContent,
            size: 'large',
            buttons: [
                {
                    text: '关闭',
                    className: 'btn-primary'
                }
            ],
            onShow: (modalElement) => {
                // Copy single card
                utils.delegate(modalElement, '.copy-card', 'click', async (e) => {
                    const card = e.target.dataset.card;
                    const success = await utils.copyToClipboard(card);
                    if (success) {
                        toast.success('卡密已复制到剪贴板');
                    } else {
                        toast.error('复制失败');
                    }
                });

                // Copy all cards
                const copyAllBtn = modalElement.querySelector('#copy-all-cards');
                copyAllBtn.addEventListener('click', async () => {
                    const allCards = cards.join('\n');
                    const success = await utils.copyToClipboard(allCards);
                    if (success) {
                        toast.success('所有卡密已复制到剪贴板');
                    } else {
                        toast.error('复制失败');
                    }
                });

                // Export cards
                const exportBtn = modalElement.querySelector('#export-cards');
                exportBtn.addEventListener('click', () => {
                    const content = cards.join('\n');
                    const blob = new Blob([content], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `cards_${days}days_${new Date().toISOString().split('T')[0]}.txt`;
                    a.click();
                    URL.revokeObjectURL(url);
                    toast.success('卡密文件已导出');
                });
            }
        });
    }

    async refreshData() {
        const refreshBtn = utils.$('#refresh-btn');
        if (refreshBtn) {
            refreshBtn.classList.add('spinning');
        }

        try {
            await this.loadDashboardData();
            toast.success('数据已刷新');
        } catch (error) {
            toast.error('刷新失败: ' + error.message);
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('spinning');
            }
        }
    }

    startAutoRefresh() {
        // Auto refresh every 5 minutes
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    destroy() {
        this.stopAutoRefresh();

        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });

        this.charts = {};
    }

    // Enhanced method to get recent activity with detailed information
    async getRecentActivity() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const [apiLogsResponse, demoLogsResponse] = await Promise.all([
                api.getLogs(today),
                api.getDemoLogs(today)
            ]);

            const apiLogs = apiLogsResponse.logs || [];
            const demoLogs = demoLogsResponse.logs || [];

            // Combine and sort all activities
            const allActivities = [];

            // Process API logs
            apiLogs.forEach(log => {
                allActivities.push({
                    time: log.timestamp,
                    type: 'api',
                    user: log.token,
                    action: this.getActionDescription(log.api),
                    api: log.api,
                    api_name: log.api_name || this.getActionDescription(log.api),
                    ip: Array.isArray(log.ips) ? log.ips[0] : 'Unknown',
                    details: log.params
                });
            });

            // Process Demo logs
            demoLogs.forEach(log => {
                allActivities.push({
                    time: log.timestamp,
                    type: 'demo',
                    user: log.card_key,
                    action: '数据查询',
                    query_type: log.type,
                    name: log.name,
                    idcard: log.idcard,
                    remaining_uses: log.remaining_uses,
                    details: log.result
                });
            });

            // Sort by timestamp (newest first) and return top 15
            allActivities.sort((a, b) => new Date(b.time) - new Date(a.time));
            return allActivities.slice(0, 15);

        } catch (error) {
            console.error('获取最近活动失败:', error);
            return [];
        }
    }

    getActionDescription(api) {
        const apiDescriptions = {
            'demo': '程序启动',
            'zhuce': '用户注册',
            'gh1': '假地址个户1',
            'gh2': '假地址个户2',
            'eys': '二要素验证',
            'lm': '姓名猎魔',
            'dqlm': '地区猎魔',
            'family': '家庭信息查询',
            'qq': 'QQ绑定查询',
            'dw': '手机号定位',
            'jz': '智慧机主',
            'zyj': '在押人员查询',
            'dujia': '独家查询',
            'kb': '身份证库补',
            'khjc': '空号检测',
            'kp': '卡泡聆听',
            'rlhy': '人脸核验',
            'sgzh': '综合社会查询',
            'wh': '网红猎魔',
            'xszc': '刑事侦查',
            'zfm': '身份证正反面处理'
        };

        return apiDescriptions[api] || '未知操作';
    }

    // Enhanced method to show detailed recent activity in dashboard
    async showRecentActivity() {
        const activities = await this.getRecentActivity();

        if (activities.length === 0) {
            return;
        }

        // Find or create recent activity section
        let activitySection = utils.$('.recent-activity');
        if (!activitySection) {
            const dashboardGrid = utils.$('.dashboard-grid');
            if (dashboardGrid) {
                activitySection = utils.createElement('div', {
                    className: 'recent-activity'
                });

                activitySection.innerHTML = `
                    <h3>最近活动</h3>
                    <div class="activity-list" id="activity-list">
                        <!-- Activities will be populated here -->
                    </div>
                `;

                dashboardGrid.appendChild(activitySection);
            }
        }

        const activityList = utils.$('#activity-list');
        if (activityList) {
            activityList.innerHTML = activities.map(activity => {
                const icon = this.getActivityIcon(activity);
                const details = this.formatActivityDetails(activity);

                return `
                    <div class="activity-item" data-type="${activity.type}">
                        <div class="activity-icon">
                            <i class="${icon}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">
                                <strong>${activity.user}</strong> ${activity.action}
                                ${details}
                            </div>
                            <div class="activity-meta">
                                <span class="activity-time">${utils.formatRelativeTime(activity.time)}</span>
                                ${activity.ip ? `<span class="activity-ip">${activity.ip}</span>` : ''}
                                ${activity.remaining_uses !== null && activity.remaining_uses !== undefined ?
                                    `<span class="activity-uses">剩余${activity.remaining_uses}次</span>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }
    }

    getActivityIcon(activity) {
        if (activity.type === 'demo') {
            return 'fas fa-search';
        }

        const iconMap = {
            'demo': 'fas fa-rocket',
            'zhuce': 'fas fa-user-plus',
            'gh1': 'fas fa-id-card',
            'gh2': 'fas fa-id-card',
            'eys': 'fas fa-shield-alt',
            'lm': 'fas fa-search',
            'dqlm': 'fas fa-map-marker-alt',
            'family': 'fas fa-users',
            'qq': 'fab fa-qq',
            'dw': 'fas fa-location-arrow',
            'jz': 'fas fa-mobile-alt',
            'zyj': 'fas fa-user-lock',
            'dujia': 'fas fa-star',
            'kb': 'fas fa-database',
            'khjc': 'fas fa-phone-slash',
            'kp': 'fas fa-headphones',
            'rlhy': 'fas fa-user-check',
            'sgzh': 'fas fa-search-plus',
            'wh': 'fas fa-video',
            'xszc': 'fas fa-gavel',
            'zfm': 'fas fa-id-badge'
        };

        return iconMap[activity.api] || 'fas fa-cog';
    }

    formatActivityDetails(activity) {
        if (activity.type === 'demo') {
            let details = '';
            if (activity.name) {
                details += ` - ${activity.name}`;
            }
            if (activity.query_type) {
                details += ` (${activity.query_type})`;
            }
            return details;
        } else if (activity.type === 'api') {
            return activity.api_name ? ` - ${activity.api_name}` : '';
        }
        return '';
    }
}

// Initialize dashboard when page is loaded
window.dashboard = new Dashboard();
