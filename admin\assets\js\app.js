// Main Application Logic

class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.pages = {};
        this.sidebarCollapsed = false;
        this.theme = 'light';
        this.themes = ['light', 'dark', 'matrix'];

        this.init();
    }

    init() {
        // Check login status
        if (!this.checkLoginStatus()) {
            window.location.href = 'login.html';
            return;
        }

        this.loadTheme();
        this.bindEvents();
        this.initSidebar();
        this.initMatrixEffects();
        this.updateUserInfo();
        this.hideLoadingScreen();

        // Initialize current page
        this.switchPage(this.currentPage);
    }

    checkLoginStatus() {
        return localStorage.getItem('admin_logged_in') === 'true';
    }

    updateUserInfo() {
        const username = localStorage.getItem('admin_username') || '管理员';
        const usernameElement = utils.$('#current-username');
        if (usernameElement) {
            usernameElement.textContent = username;
        }
    }

    bindEvents() {
        // Sidebar navigation
        utils.delegate(document, '.nav-item', 'click', (e) => {
            e.preventDefault();
            const page = e.target.closest('.nav-item').dataset.page;
            if (page) {
                this.switchPage(page);
            }
        });

        // Sidebar toggle
        const sidebarToggle = utils.$('#sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Mobile menu toggle
        const mobileMenuToggle = utils.$('#mobile-menu-toggle');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                this.toggleMobileSidebar();
            });
        }

        // Theme toggle
        const themeToggle = utils.$('#theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // User menu toggle
        const userMenuToggle = utils.$('.user-menu-toggle');
        if (userMenuToggle) {
            userMenuToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleUserMenu();
            });
        }

        // Close user menu when clicking outside
        document.addEventListener('click', (e) => {
            const userMenu = utils.$('.user-menu');
            if (userMenu && !userMenu.contains(e.target)) {
                userMenu.classList.remove('active');
            }
        });



        // Logout functionality
        utils.delegate(document, '.dropdown-item', 'click', (e) => {
            const href = e.target.getAttribute('href');
            if (href === '#' && e.target.textContent.includes('退出登录')) {
                e.preventDefault();
                this.logout();
            }
        });

        // Close mobile sidebar when clicking overlay
        utils.delegate(document, '.sidebar-overlay', 'click', () => {
            this.closeMobileSidebar();
        });

        // Handle window resize
        window.addEventListener('resize', utils.throttle(() => {
            this.handleResize();
        }, 250));

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    switchPage(pageName) {
        if (this.currentPage === pageName) return;

        // Update navigation
        utils.$$('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeNavItem = utils.$(`[data-page="${pageName}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }

        // Update page title
        const pageTitle = utils.$('#page-title');
        const pageTitles = {
            dashboard: '仪表盘',
            users: '用户管理',
            cards: '卡密管理',
            settings: '系统设置',
            statistics: '数据统计',
            logs: '操作日志'
        };

        if (pageTitle) {
            pageTitle.textContent = pageTitles[pageName] || '未知页面';
        }

        // Hide all pages
        utils.$$('.page').forEach(page => {
            page.classList.remove('active');
        });

        // Show target page
        const targetPage = utils.$(`#${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Load page content if not loaded
        this.loadPage(pageName);

        // Update current page
        this.currentPage = pageName;

        // Close mobile sidebar
        this.closeMobileSidebar();

        // Update URL
        utils.url.setParam('page', pageName);
    }

    async loadPage(pageName) {
        if (this.pages[pageName]) {
            // Page already loaded
            return;
        }

        try {
            switch (pageName) {
                case 'dashboard':
                    // Dashboard is already initialized
                    this.pages[pageName] = window.dashboard;
                    break;
                    
                case 'users':
                    if (window.UsersPage) {
                        this.pages[pageName] = new window.UsersPage();
                    }
                    break;
                    
                case 'cards':
                    if (window.CardsPage) {
                        this.pages[pageName] = new window.CardsPage();
                    }
                    break;
                    
                case 'settings':
                    if (window.SettingsPage) {
                        this.pages[pageName] = new window.SettingsPage();
                    }
                    break;
                    
                case 'statistics':
                    if (window.StatisticsPage) {
                        this.pages[pageName] = new window.StatisticsPage();
                    }
                    break;
                    
                case 'logs':
                    if (window.LogsPage) {
                        this.pages[pageName] = new window.LogsPage();
                    }
                    break;
            }
        } catch (error) {
            console.error(`Failed to load page ${pageName}:`, error);
            toast.error(`加载页面失败: ${error.message}`);
        }
    }

    initSidebar() {
        // Load sidebar state from storage
        const collapsed = utils.storage.get('sidebar-collapsed', false);
        if (collapsed) {
            this.toggleSidebar(false);
        }
    }

    toggleSidebar(save = true) {
        const sidebar = utils.$('#sidebar');
        if (!sidebar) return;

        this.sidebarCollapsed = !this.sidebarCollapsed;
        sidebar.classList.toggle('collapsed', this.sidebarCollapsed);

        if (save) {
            utils.storage.set('sidebar-collapsed', this.sidebarCollapsed);
        }
    }

    toggleMobileSidebar() {
        const sidebar = utils.$('#sidebar');
        if (!sidebar) return;

        const isOpen = sidebar.classList.contains('mobile-open');
        
        if (isOpen) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }

    openMobileSidebar() {
        const sidebar = utils.$('#sidebar');
        if (!sidebar) return;

        // Create overlay if it doesn't exist
        let overlay = utils.$('.sidebar-overlay');
        if (!overlay) {
            overlay = utils.createElement('div', {
                className: 'sidebar-overlay'
            });
            document.body.appendChild(overlay);
        }

        sidebar.classList.add('mobile-open');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeMobileSidebar() {
        const sidebar = utils.$('#sidebar');
        const overlay = utils.$('.sidebar-overlay');

        if (sidebar) {
            sidebar.classList.remove('mobile-open');
        }

        if (overlay) {
            overlay.classList.remove('active');
        }

        document.body.style.overflow = '';
    }

    loadTheme() {
        this.theme = utils.storage.get('theme', 'light');
        this.applyTheme();
    }

    toggleTheme() {
        const currentIndex = this.themes.indexOf(this.theme);
        const nextIndex = (currentIndex + 1) % this.themes.length;
        this.theme = this.themes[nextIndex];
        this.applyTheme();
        utils.storage.set('theme', this.theme);
    }

    toggleUserMenu() {
        const userMenu = utils.$('.user-menu');
        if (userMenu) {
            userMenu.classList.toggle('active');
        }
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);

        const themeToggle = utils.$('#theme-toggle i');
        if (themeToggle) {
            const icons = {
                'light': 'fas fa-moon',
                'dark': 'fas fa-sun',
                'matrix': 'fas fa-terminal'
            };
            themeToggle.className = icons[this.theme] || 'fas fa-moon';
        }

        // Update theme toggle title
        const themeToggleBtn = utils.$('#theme-toggle');
        if (themeToggleBtn) {
            const titles = {
                'light': '切换到暗色主题',
                'dark': '切换到Matrix主题',
                'matrix': '切换到亮色主题'
            };
            themeToggleBtn.title = titles[this.theme] || '切换主题';
        }
    }

    handleResize() {
        const width = window.innerWidth;
        
        // Auto-collapse sidebar on tablet
        if (width <= 992 && !this.sidebarCollapsed) {
            this.toggleSidebar(false);
        }
        
        // Close mobile sidebar on desktop
        if (width > 768) {
            this.closeMobileSidebar();
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K: Focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = utils.$('.search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Ctrl/Cmd + B: Toggle sidebar
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebar();
        }

        // Ctrl/Cmd + T: Toggle theme
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            this.toggleTheme();
        }

        // Escape: Close modals/menus
        if (e.key === 'Escape') {
            const userMenu = utils.$('.user-menu');
            if (userMenu && userMenu.classList.contains('active')) {
                userMenu.classList.remove('active');
            }

            this.closeMobileSidebar();
        }

        // Number keys: Switch pages
        if (e.altKey && e.key >= '1' && e.key <= '6') {
            e.preventDefault();
            const pages = ['dashboard', 'users', 'cards', 'settings', 'statistics', 'logs'];
            const pageIndex = parseInt(e.key) - 1;
            if (pages[pageIndex]) {
                this.switchPage(pages[pageIndex]);
            }
        }
    }

    hideLoadingScreen() {
        const loadingScreen = utils.$('#loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 1000);
        }
    }

    // Public methods for other modules
    getCurrentPage() {
        return this.currentPage;
    }

    getPageInstance(pageName) {
        return this.pages[pageName];
    }

    refreshCurrentPage() {
        const pageInstance = this.pages[this.currentPage];
        if (pageInstance && typeof pageInstance.refresh === 'function') {
            pageInstance.refresh();
        }
    }

    async logout() {
        const confirmed = await modal.confirm('确定要退出登录吗？', '退出登录');
        if (confirmed) {
            localStorage.removeItem('admin_logged_in');
            localStorage.removeItem('admin_username');
            toast.success('已退出登录');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);
        }
    }

    initMatrixEffects() {
        // Matrix effects toggle
        const matrixToggle = utils.$('#matrix-toggle');
        if (matrixToggle) {
            matrixToggle.addEventListener('click', () => {
                if (window.matrixEffects) {
                    window.matrixEffects.toggleEffects();
                    const icon = matrixToggle.querySelector('i');
                    if (icon.classList.contains('fa-eye')) {
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                }
            });
        }

        // Refresh button enhancement
        const refreshBtn = utils.$('#refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCurrentPage();
                // Add visual feedback
                const icon = refreshBtn.querySelector('i');
                icon.style.animation = 'spin 1s linear';
                setTimeout(() => {
                    icon.style.animation = '';
                }, 1000);
            });
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// Handle page visibility change
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.app) {
        // Page became visible, refresh current page
        setTimeout(() => {
            window.app.refreshCurrentPage();
        }, 1000);
    }
});
