#!/bin/bash

# 创建日志目录
mkdir -p logs

# 获取当前时间作为日志文件名
current_time=$(date +"%Y%m%d_%H%M%S")
log_file="logs/bot_${current_time}.log"

# 检查PID文件是否存在
if [ -f bot.pid ]; then
    old_pid=$(cat bot.pid)
    # 检查旧进程是否还在运行
    if ps -p $old_pid > /dev/null 2>&1; then
        echo "发现旧进程（PID: $old_pid）正在运行，正在停止..."
        kill $old_pid
        sleep 2
        if ps -p $old_pid > /dev/null 2>&1; then
            kill -9 $old_pid
        fi
        rm bot.pid
    else
        rm bot.pid
    fi
fi

# 启动机器人并将输出重定向到日志文件
nohup python3 bot.py > "$log_file" 2>&1 &

# 保存进程ID
echo $! > bot.pid

# 等待几秒检查进程是否成功启动
sleep 3
if ps -p $(cat bot.pid) > /dev/null 2>&1; then
    echo "机器人已成功启动！"
    echo "日志文件：$log_file"
    echo "进程ID：$(cat bot.pid)"
else
    echo "机器人启动失败，请检查日志文件：$log_file"
    rm bot.pid
    exit 1
fi 