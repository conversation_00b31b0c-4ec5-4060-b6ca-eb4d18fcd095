<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单API测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; cursor: pointer; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .success { background: #d4edda; border-color: #c3e6cb; }
    </style>
</head>
<body>
    <h1>简单API测试</h1>
    
    <button class="test-btn" onclick="testAPI('todaystats.php')">测试今日统计</button>
    <button class="test-btn" onclick="testAPI('cardstats.php?status=stats')">测试卡密统计</button>
    <button class="test-btn" onclick="testAPI('userstats.php?type=stats')">测试用户统计</button>
    <button class="test-btn" onclick="testAPI('logstats.php?type=api')">测试日志统计</button>
    
    <div id="results"></div>

    <script>
        async function testAPI(endpoint) {
            const resultsDiv = document.getElementById('results');
            
            try {
                const response = await fetch(`../admapi/${endpoint}`);
                const text = await response.text();
                
                let resultClass = 'result';
                let resultContent = '';
                
                try {
                    const json = JSON.parse(text);
                    resultClass += ' success';
                    resultContent = `
                        <h3>✅ ${endpoint} - 成功</h3>
                        <pre>${JSON.stringify(json, null, 2)}</pre>
                    `;
                } catch (e) {
                    resultClass += ' error';
                    resultContent = `
                        <h3>❌ ${endpoint} - JSON解析失败</h3>
                        <p>返回的不是有效的JSON:</p>
                        <pre>${text}</pre>
                    `;
                }
                
                const resultDiv = document.createElement('div');
                resultDiv.className = resultClass;
                resultDiv.innerHTML = resultContent;
                
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
                
            } catch (error) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ ${endpoint} - 网络错误</h3>
                    <p>${error.message}</p>
                `;
                
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
            }
        }
    </script>
</body>
</html>
