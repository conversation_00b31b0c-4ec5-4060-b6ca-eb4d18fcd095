# 管理后台 .htaccess 配置

# 启用重写引擎
RewriteEngine On

# 默认首页重定向
DirectoryIndex demo.html

# 安全设置
# 禁止访问敏感文件
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# 设置缓存策略
<IfModule mod_expires.c>
    ExpiresActive On
    
    # CSS 和 JS 文件缓存 1 个月
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # 图片文件缓存 1 年
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    
    # HTML 文件缓存 1 小时
    ExpiresByType text/html "access plus 1 hour"
    
    # 字体文件缓存 1 年
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
</IfModule>

# 启用 Gzip 压缩
<IfModule mod_deflate.c>
    # 压缩文本文件
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/json
    
    # 排除已压缩的文件
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|swf|woff|woff2)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# 设置 MIME 类型
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType font/woff .woff
    AddType font/woff2 .woff2
</IfModule>

# 安全头设置
<IfModule mod_headers.c>
    # 防止点击劫持
    Header always append X-Frame-Options SAMEORIGIN
    
    # 防止 MIME 类型嗅探
    Header always set X-Content-Type-Options nosniff
    
    # XSS 保护
    Header always set X-XSS-Protection "1; mode=block"
    
    # 引用策略
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # 内容安全策略（根据需要调整）
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# 错误页面
ErrorDocument 404 /admin/404.html
ErrorDocument 403 /admin/403.html
ErrorDocument 500 /admin/500.html

# URL 重写规则
# 如果访问根目录，重定向到演示页面
RewriteRule ^$ demo.html [L]

# 如果访问 /admin，重定向到演示页面
RewriteRule ^admin/?$ demo.html [L]

# 友好的 URL 重写
RewriteRule ^dashboard/?$ index.html [L]
RewriteRule ^login/?$ login.html [L]
RewriteRule ^demo/?$ demo.html [L]
