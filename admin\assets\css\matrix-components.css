/* Matrix Components CSS */

/* Matrix Dashboard Components */
[data-theme="matrix"] .dashboard-grid {
    position: relative;
}

[data-theme="matrix"] .dashboard-grid::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 50%, rgba(0, 255, 65, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(0, 255, 65, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Matrix Stats Cards */
[data-theme="matrix"] .stat-card {
    position: relative;
    overflow: hidden;
}

[data-theme="matrix"] .stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg, transparent, var(--matrix-green), transparent);
    animation: matrixVerticalScan 3s linear infinite;
}

@keyframes matrixVerticalScan {
    0%, 100% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

/* Matrix Chart Cards */
[data-theme="matrix"] .chart-card {
    position: relative;
    border: 1px solid var(--matrix-border);
}

[data-theme="matrix"] .chart-card::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, var(--matrix-green), transparent, var(--matrix-green));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

[data-theme="matrix"] .chart-card:hover::before {
    opacity: 0.3;
}

[data-theme="matrix"] .card-header {
    background: var(--matrix-bg-darker);
    border-bottom: 1px solid var(--matrix-border);
    position: relative;
}

[data-theme="matrix"] .card-header h3 {
    color: var(--matrix-green);
    text-shadow: 0 0 10px var(--matrix-green);
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

/* Matrix Quick Actions */
[data-theme="matrix"] .quick-actions {
    position: relative;
}

[data-theme="matrix"] .quick-actions h3 {
    color: var(--matrix-green);
    text-shadow: 0 0 10px var(--matrix-green);
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

[data-theme="matrix"] .quick-actions h3::before {
    content: '> ';
    color: var(--matrix-green);
    animation: matrixBlink 1s infinite;
}

@keyframes matrixBlink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Matrix Action Buttons */
[data-theme="matrix"] .action-btn {
    position: relative;
    overflow: hidden;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    text-transform: uppercase;
}

[data-theme="matrix"] .action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

[data-theme="matrix"] .action-btn:hover::before {
    left: 100%;
}

/* Matrix Tables */
[data-theme="matrix"] .table-container {
    position: relative;
    border: 1px solid var(--matrix-border);
}

[data-theme="matrix"] .table-header {
    background: var(--matrix-bg-darker);
    border-bottom: 1px solid var(--matrix-border);
}

[data-theme="matrix"] .table-title {
    color: var(--matrix-green);
    text-shadow: 0 0 10px var(--matrix-green);
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

[data-theme="matrix"] .data-table {
    font-family: 'Courier New', monospace;
}

[data-theme="matrix"] .data-table th {
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: bold;
}

[data-theme="matrix"] .data-table tbody tr {
    transition: all 0.3s ease;
}

[data-theme="matrix"] .data-table tbody tr:hover {
    background: rgba(0, 255, 65, 0.1);
    box-shadow: inset 0 0 10px rgba(0, 255, 65, 0.2);
}

/* Matrix Status Indicators */
[data-theme="matrix"] .status-badge {
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

[data-theme="matrix"] .status-active {
    background: rgba(0, 255, 65, 0.2);
    color: var(--matrix-green);
    border: 1px solid var(--matrix-green);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

[data-theme="matrix"] .status-active::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 4px;
    width: 4px;
    height: 4px;
    background: var(--matrix-green);
    border-radius: 50%;
    animation: matrixPulse 1s infinite;
}

@keyframes matrixPulse {
    0%, 100% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 0.5;
        transform: translateY(-50%) scale(1.2);
    }
}

/* Matrix Forms */
[data-theme="matrix"] .form-group {
    position: relative;
}

[data-theme="matrix"] .form-label {
    color: var(--matrix-green);
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 12px;
}

[data-theme="matrix"] .form-control {
    font-family: 'Courier New', monospace;
    background: rgba(0, 255, 65, 0.05);
    border: 1px solid var(--matrix-border);
    transition: all 0.3s ease;
}

[data-theme="matrix"] .form-control:focus {
    background: rgba(0, 255, 65, 0.1);
    border-color: var(--matrix-green);
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
    color: var(--matrix-green);
}

/* Matrix Search Box */
[data-theme="matrix"] .search-box {
    position: relative;
}

[data-theme="matrix"] .search-box::before {
    content: 'SEARCH:';
    position: absolute;
    top: -20px;
    left: 0;
    font-size: 10px;
    color: var(--matrix-green);
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

/* Matrix Pagination */
[data-theme="matrix"] .table-pagination {
    background: var(--matrix-bg-darker);
    border-top: 1px solid var(--matrix-border);
}

[data-theme="matrix"] .pagination-info {
    color: var(--matrix-text-dim);
    font-family: 'Courier New', monospace;
}

[data-theme="matrix"] .pagination-btn {
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid var(--matrix-border);
    color: var(--matrix-text-dim);
    font-family: 'Courier New', monospace;
}

[data-theme="matrix"] .pagination-btn:hover:not(:disabled) {
    background: rgba(0, 255, 65, 0.2);
    border-color: var(--matrix-green);
    color: var(--matrix-green);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

[data-theme="matrix"] .pagination-btn:disabled {
    opacity: 0.3;
    background: rgba(0, 255, 65, 0.05);
}

/* Matrix Modals */
[data-theme="matrix"] .modal-overlay {
    background: rgba(0, 0, 0, 0.8);
}

[data-theme="matrix"] .modal {
    border: 1px solid var(--matrix-green);
    box-shadow: 0 0 30px rgba(0, 255, 65, 0.5);
}

[data-theme="matrix"] .modal-header {
    background: var(--matrix-bg-darker);
    border-bottom: 1px solid var(--matrix-border);
}

[data-theme="matrix"] .modal-title {
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
}

[data-theme="matrix"] .modal-close {
    color: var(--matrix-text-dim);
    transition: all 0.3s ease;
}

[data-theme="matrix"] .modal-close:hover {
    color: var(--matrix-green);
    background: rgba(0, 255, 65, 0.1);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

/* Matrix Toast Notifications */
[data-theme="matrix"] .toast {
    border: 1px solid var(--matrix-border);
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    font-family: 'Courier New', monospace;
}

[data-theme="matrix"] .toast-title {
    color: var(--matrix-green);
    text-transform: uppercase;
    letter-spacing: 1px;
}

[data-theme="matrix"] .toast-message {
    color: var(--matrix-text-dim);
}

/* Matrix Loading Overlay */
[data-theme="matrix"] .loading-overlay {
    background: rgba(0, 0, 0, 0.9);
}

[data-theme="matrix"] .loading-content {
    background: var(--matrix-bg-dark);
    border: 1px solid var(--matrix-green);
    box-shadow: 0 0 30px rgba(0, 255, 65, 0.5);
}

[data-theme="matrix"] .loading-content p {
    color: var(--matrix-green);
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* Matrix Code Elements */
[data-theme="matrix"] .user-token,
[data-theme="matrix"] .card-code,
[data-theme="matrix"] .log-time,
[data-theme="matrix"] .ip-address,
[data-theme="matrix"] .id-card {
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid var(--matrix-border);
    color: var(--matrix-green);
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 5px var(--matrix-green);
}

/* Matrix Activity Items */
[data-theme="matrix"] .activity-item {
    background: rgba(0, 255, 65, 0.05);
    border-left: 3px solid var(--matrix-green);
    border-radius: 0;
}

[data-theme="matrix"] .activity-item:hover {
    background: rgba(0, 255, 65, 0.1);
    box-shadow: inset 0 0 10px rgba(0, 255, 65, 0.2);
}

[data-theme="matrix"] .activity-icon {
    background: linear-gradient(135deg, var(--matrix-green), var(--matrix-dark-green));
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.5);
}

[data-theme="matrix"] .activity-text strong {
    color: var(--matrix-green);
    text-shadow: 0 0 5px var(--matrix-green);
}
