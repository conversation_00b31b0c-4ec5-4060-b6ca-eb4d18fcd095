<?php
/**
 * 管理后台配置文件
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入数据库配置
require_once '../db.php';

/**
 * 返回JSON响应
 */
function jsonResponse($code, $message, $data = null) {
    $response = [
        'code' => $code,
        'message' => $message
    ];
    
    if ($data !== null) {
        if (is_array($data) && isset($data['users'])) {
            $response['users'] = $data['users'];
        } elseif (is_array($data) && isset($data['kamis'])) {
            $response['kamis'] = $data['kamis'];
        } elseif (is_array($data) && isset($data['msg'])) {
            $response['msg'] = $data['msg'];
        } else {
            $response['data'] = $data;
        }
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

/**
 * 获取请求参数
 */
function getParam($key, $default = null) {
    if (isset($_GET[$key])) {
        return $_GET[$key];
    }
    if (isset($_POST[$key])) {
        return $_POST[$key];
    }
    return $default;
}

/**
 * 验证管理员权限（简单实现）
 */
function checkAdminAuth() {
    // 这里可以添加更复杂的权限验证逻辑
    // 目前为了演示，暂时跳过验证
    return true;
}

/**
 * 记录操作日志
 */
function logAdminOperation($operation, $details = '') {
    $logFile = '../admin_logs/admin_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'operation' => $operation,
        'details' => $details,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    file_put_contents($logFile, json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * 获取客户端IP
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * 安全地执行数据库查询
 */
function safeQuery($sql, $params = []) {
    global $conn;
    
    try {
        $stmt = $conn->prepare($sql);
        if ($params) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        return $stmt;
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        jsonResponse(500, "数据库操作失败");
    }
}

/**
 * 格式化用户数据
 */
function formatUserData($users) {
    return array_map(function($user) {
        return [
            'token' => $user['token'] ?? '',
            'time' => $user['time'] ?? '',
            'vipcode' => $user['vipcode'] ?? '0',
            'viptime' => $user['viptime'] ?? '',
            'tokencode' => $user['tokencode'] ?? '200'
        ];
    }, $users);
}

/**
 * 格式化卡密数据
 */
function formatCardData($cards) {
    return array_map(function($card) {
        return [
            'kami' => $card['kami'] ?? '',
            'kamitime' => $card['kamitime'] ?? '',
            'kamicode' => $card['kamicode'] ?? '0',
            'oktoken' => $card['oktoken'] ?? '',
            'oktime' => $card['oktime'] ?? ''
        ];
    }, $cards);
}

/**
 * 验证输入参数
 */
function validateInput($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;
        
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[] = "字段 {$field} 是必需的";
            continue;
        }
        
        if (!empty($value)) {
            if (isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[] = "字段 {$field} 必须是有效的邮箱地址";
                        }
                        break;
                    case 'url':
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            $errors[] = "字段 {$field} 必须是有效的URL";
                        }
                        break;
                    case 'int':
                        if (!filter_var($value, FILTER_VALIDATE_INT)) {
                            $errors[] = "字段 {$field} 必须是整数";
                        }
                        break;
                }
            }
            
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[] = "字段 {$field} 长度不能少于 {$rule['min_length']} 个字符";
            }
            
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[] = "字段 {$field} 长度不能超过 {$rule['max_length']} 个字符";
            }
        }
    }
    
    return $errors;
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * 检查数据库连接
 */
function checkDatabaseConnection() {
    global $conn;
    
    if (!$conn) {
        jsonResponse(500, "数据库连接失败");
    }
    
    try {
        $conn->query("SELECT 1");
    } catch (PDOException $e) {
        jsonResponse(500, "数据库连接异常: " . $e->getMessage());
    }
}

// 检查数据库连接
checkDatabaseConnection();

// 验证管理员权限
if (!checkAdminAuth()) {
    jsonResponse(403, "权限不足");
}
?>
