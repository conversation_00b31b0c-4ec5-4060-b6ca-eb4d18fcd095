<?php
require '../db.php';

// 获取GET请求的参数
$appvid = isset($_GET['appvid']) ? $_GET['appvid'] : null;
$appcode = isset($_GET['appcode']) ? $_GET['appcode'] : null;
$appgg = isset($_GET['appgg']) ? $_GET['appgg'] : null;
$appurl = isset($_GET['appurl']) ? $_GET['appurl'] : null;
$appimg = isset($_GET['appimg']) ? $_GET['appimg'] : null;
$dailiurl = isset($_GET['dailiurl']) ? $_GET['dailiurl'] : null;
$qqqid = isset($_GET['qqqid']) ? $_GET['qqqid'] : null;

// 构建更新SQL语句
$updates = [];
if ($appvid !== null) $updates[] = "appvid='$appvid'";
if ($appcode !== null) $updates[] = "appcode='$appcode'";
if ($appgg !== null) $updates[] = "appgg='$appgg'";
if ($appurl !== null) $updates[] = "appurl='$appurl'";
if ($appimg !== null) $updates[] = "appimg='$appimg'";
if ($dailiurl !== null) $updates[] = "dailiurl='$dailiurl'";
if ($qqqid !== null) $updates[] = "qqqid='$qqqid'";

// 检查是否有更新字段
if (count($updates) > 0) {
    $sql = "UPDATE msg SET " . implode(", ", $updates);
    
    // 执行SQL语句
    if ($mysqli->query($sql) === TRUE) {
        // 获取更新后的信息
        $result = $mysqli->query("SELECT * FROM msg LIMIT 1");
        $appInfo = $result->fetch_assoc();
        echo json_encode([
            "code" => 200,
            "message" => "设置成功",
            "msg" => $appInfo
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            "code" => 500,
            "message" => "更新失败: " . $mysqli->error,
            "msg" => null
        ], JSON_UNESCAPED_UNICODE);
    }
} else {
    echo json_encode([
        "code" => 400,
        "message" => "没有任何更新",
        "msg" => null
    ], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
