<?php
/**
 * 日志统计API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $date = $_GET['date'] ?? date('Y-m-d');
    $type = $_GET['type'] ?? 'api';
    
    $response = [
        'code' => 200,
        'message' => 'success'
    ];
    
    // API名称映射
    $apiNames = [
        'demo' => '程序启动',
        'zhuce' => '用户注册',
        'gh1' => '假地址个户1',
        'gh2' => '假地址个户2',
        'eys' => '二要素验证',
        'lm' => '姓名猎魔',
        'dqlm' => '地区猎魔',
        'family' => '家庭信息',
        'qq' => 'QQ绑定查询',
        'dw' => '手机号定位',
        'jz' => '智慧机主',
        'zyj' => '在押人员',
        'dujia' => '独家查询',
        'kb' => '身份证库补',
        'khjc' => '空号检测',
        'kp' => '卡泡聆听',
        'rlhy' => '人脸核验',
        'sgzh' => '综合社会查询',
        'wh' => '网红猎魔',
        'xszc' => '刑事侦查',
        'zfm' => '身份证正反面处理'
    ];
    
    if ($type === 'api') {
        // 分析API日志
        $logFile = "../api_logs/api_log_{$date}.txt";
        $logs = [];
        
        if (file_exists($logFile)) {
            $content = file_get_contents($logFile);
            $lines = explode("\n", $content);
            
            foreach ($lines as $line) {
                if (empty(trim($line))) continue;
                
                // 解析日志格式
                if (preg_match('/^\[([^\]]+)\]\s+IP:\s+([^,]+(?:,\s*[^,]+)*),\s+Token:\s+([^,]+),\s+API:\s+([^,]+),\s+Params:\s+(.+)$/', $line, $matches)) {
                    $timestamp = $matches[1];
                    $ips = array_map('trim', explode(',', $matches[2]));
                    $token = $matches[3];
                    $api = $matches[4];
                    $paramsStr = $matches[5];
                    
                    try {
                        $params = json_decode($paramsStr, true);
                    } catch (Exception $e) {
                        $params = ['raw' => $paramsStr];
                    }
                    
                    $logs[] = [
                        'timestamp' => $timestamp,
                        'ips' => $ips,
                        'token' => $token,
                        'api' => $api,
                        'api_name' => $apiNames[$api] ?? $api,
                        'params' => $params
                    ];
                }
            }
        }
        
        $response['logs'] = $logs;
        $response['count'] = count($logs);
        
    } elseif ($type === 'demo') {
        // 分析Demo日志
        $logFile = "../api_logs/demo_api_log_{$date}.txt";
        $logs = [];
        
        if (file_exists($logFile)) {
            $content = file_get_contents($logFile);
            $lines = explode("\n", $content);
            
            foreach ($lines as $line) {
                if (empty(trim($line))) continue;
                
                try {
                    $data = json_decode($line, true);
                    if ($data) {
                        $logs[] = [
                            'timestamp' => $data['timestamp'] ?? '',
                            'card_key' => $data['card_key'] ?? '',
                            'name' => $data['name'] ?? '',
                            'idcard' => $data['idcard'] ?? '',
                            'type' => $data['lx'] ?? '',
                            'result' => $data['result'] ?? null,
                            'remaining_uses' => isset($data['result']['remaining_uses']) ? $data['result']['remaining_uses'] : null
                        ];
                    }
                } catch (Exception $e) {
                    // 忽略解析错误的行
                }
            }
        }
        
        $response['logs'] = $logs;
        $response['count'] = count($logs);
        
    } else {
        // 简单统计
        $stats = [
            'date' => $date,
            'api_count' => 0,
            'demo_count' => 0
        ];
        
        // 统计API日志
        $apiLogFile = "../api_logs/api_log_{$date}.txt";
        if (file_exists($apiLogFile)) {
            $content = file_get_contents($apiLogFile);
            $stats['api_count'] = substr_count($content, '[');
        }
        
        // 统计Demo日志
        $demoLogFile = "../api_logs/demo_api_log_{$date}.txt";
        if (file_exists($demoLogFile)) {
            $content = file_get_contents($demoLogFile);
            $lines = explode("\n", trim($content));
            $stats['demo_count'] = count(array_filter($lines));
        }
        
        $response['stats'] = $stats;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'code' => 500,
        'message' => '获取日志数据失败: ' . $e->getMessage()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>
