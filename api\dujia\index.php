<?php
require 'vendor/autoload.php';
require 'address.php';
require 'names.php';  // 添加姓名库引用

use Jxlwqq\IdValidator\IdValidator;  // 修改命名空间大小写

// 允许跨域访问
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');



require_once '../../verify_vip.php';


// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = true;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}



// 获取请求参数
$action = $_GET['action'] ?? '';
$name = $_GET['name'] ?? '';
$idcard = $_GET['idcard'] ?? '';
$lx = $_GET['lx'] ?? '1';
$wechat = $_GET['wechat'] ?? '';
$douyin = $_GET['douyin'] ?? '';
$kuaishou = $_GET['kuaishou'] ?? '';

// 根据action参数执行不同的功能
switch ($action) {
    case 'generate_id':
        // 社交平台账号生成身份信息
        $platform = '';
        $account = '';
        
        if (!empty($wechat)) {
            $platform = '微信';
            $account = $wechat;
        } elseif (!empty($douyin)) {
            $platform = '抖音';
            $account = $douyin;
        } elseif (!empty($kuaishou)) {
            $platform = '快手';
            $account = $kuaishou;
        }
        
        if (empty($platform)) {
            echo json_encode(['code' => 400, 'message' => '请提供社交平台账号']);
            exit;
        }

        try {
            // 生成随机姓名
            $xing = $arrXing[array_rand($arrXing)];
            $ming = $arrMing[array_rand($arrMing)];
            $name = $xing . $ming;

            // 生成随机身份证号
            $validator = new IdValidator();
            $idCard = $validator->fakeId();

            echo json_encode([
                'code' => 200,
                'data' => [
                    'name' => $name,
                    'idcard' => $idCard
                ]
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'code' => 500,
                'message' => $e->getMessage()
            ]);
        }
        exit;

    case 'generate_marriage':
        // 生成婚姻史信息
        if (empty($name) || empty($idcard)) {
            echo json_encode(['code' => 400, 'message' => '姓名和身份证号不能为空']);
            exit;
        }

        try {
            // 验证身份证号
            $validator = new IdValidator();
            if (!$validator->isValid($idcard)) {
                echo json_encode(['code' => 400, 'message' => '身份证号格式不正确']);
                exit;
            }

            // 生成婚姻史信息
            $marriageHistory = generateMarriageHistory($name, $idcard);
            
            echo json_encode([
                'code' => 200,
                'data' => $marriageHistory
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'code' => 500,
                'message' => $e->getMessage()
            ]);
        }
        exit;

    case 'platform_id':
        // 平台账号返二要素功能，支持微信、抖音、快手
        $platform = '';
        $account = '';
        if (!empty($wechat)) {
            $platform = 'wechat';
            $account = $wechat;
        } elseif (!empty($douyin)) {
            $platform = 'douyin';
            $account = $douyin;
        } elseif (!empty($kuaishou)) {
            $platform = 'kuaishou';
            $account = $kuaishou;
        }
        if (empty($name) || empty($platform) || empty($account)) {
            echo json_encode(['code' => 400, 'message' => '姓名和平台账号不能为空'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            exit;
        }
        $persistFile = 'platform_id_data.json';
        $persistData = [];
        if (file_exists($persistFile)) {
            $persistData = json_decode(file_get_contents($persistFile), true) ?? [];
        }
        $key = md5($platform . '_' . $name . '_' . $account);
        if (isset($persistData[$key])) {
            // 已有记录，直接返回
            $data = $persistData[$key];
            echo json_encode([
                'code' => 200,
                'data' => $data
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            exit;
        }
        // 40%概率返回不匹配
        if (mt_rand(1, 100) <= 40) {
            echo json_encode([
                'code' => 400,
                'message' => '姓名与账号不匹配,请核对'
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            exit;
        }
        // 生成身份证号
        $validator = new IdValidator();
        $idcard = $validator->fakeId();
        $data = [
            'name' => $name,
            'platform' => $platform,
            'account' => $account,
            'idcard' => $idcard
        ];
        $persistData[$key] = $data;
        file_put_contents($persistFile, json_encode($persistData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        echo json_encode([
            'code' => 200,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;

    default:
        // 默认的查询功能
        if (empty($name) || empty($idcard)) {
            echo json_encode([
                'code' => 400,
                'message' => '姓名和身份证号不能为空',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }

        try {
            // 初始化身份证验证器
            $idValidator = new IdValidator();
            
            // 验证身份证号
            if (!$idValidator->isValid($idcard)) {
                echo json_encode([
                    'code' => 400,
                    'message' => '身份证号格式不正确',
                    'data' => null
                ], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            // 获取身份证信息
            $info = $idValidator->getInfo($idcard);
            
            // 检查是否存在持久化数据
            $persistFile = 'persist_data.json';
            $persistData = [];
            if (file_exists($persistFile)) {
                $persistData = json_decode(file_get_contents($persistFile), true) ?? [];
            }
            
            // 如果该身份证号已有记录，使用已存在的数据
            if (isset($persistData[$idcard])) {
                $fakeAddress = $persistData[$idcard]['fake_address'];
                $randomAvatar = $persistData[$idcard]['avatar'];
            } else {
                // 生成新的假地址和头像
                $fakeAddress = generateAddress($idcard);
                $sex = $info['sex'] === 1 ? '男' : '女';
                $genderDir = $sex === '男' ? 'dt/男/' : 'dt/女/';
                $avatars = glob($genderDir . '*.jpg');
                $randomAvatar = $avatars[array_rand($avatars)];
                
                // 保存新生成的数据
                $persistData[$idcard] = [
                    'fake_address' => $fakeAddress,
                    'avatar' => $randomAvatar
                ];
                file_put_contents($persistFile, json_encode($persistData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            }
            
            // 获取真实省市区地址和其他信息
            $realAddress = $info['address'];
            $birthdayCode = $info['birthdayCode'];
            $sex = $info['sex'] === 1 ? '男' : '女';
            
            // 加载表格图片
            $tableImage = imagecreatefrompng('bg.png');
            if ($tableImage === false) {
                throw new Exception('无法加载表格图片');
            }
            
            $tableWidth = imagesx($tableImage);
            $tableHeight = imagesy($tableImage);
            
            // 计算背景高度（表格高度 + 文字区域高度 + 上下边距）
            $textHeight = 300; // 预留文字区域高度
            $margin = 100; // 上下边距
            $bgWidth = $tableWidth + 200; // 左右边距各100
            $bgHeight = $tableHeight + $textHeight + $margin * 2;
            
            // 创建纯白背景
            $image = imagecreatetruecolor($bgWidth, $bgHeight);
            $white = imagecolorallocate($image, 255, 255, 255);
            imagefill($image, 0, 0, $white);
            
            // 计算表格图片在背景中的位置（居中）
            $tableX = ($bgWidth - $tableWidth) / 2;
            $tableY = $margin; // 距离顶部边距
            
            // 将表格图片绘制到背景上
            imagecopy($image, $tableImage, $tableX, $tableY, 0, 0, $tableWidth, $tableHeight);
            imagedestroy($tableImage);
            
            // 根据类型选择不同的底图
            // $bgImage = $lx == '1' ? 'bg_qisu.png' : 'bg_huji.png';
            
            // 读取背景图片和头像
            // $image = imagecreatefrompng($bgImage);
            
            // 加载头像图片（支持多种格式）
            function loadImage($file) {
                $image = null;
                $info = getimagesize($file);
                if ($info === false) {
                    return false;
                }
                
                switch ($info[2]) {
                    case IMAGETYPE_JPEG:
                        $image = imagecreatefromjpeg($file);
                        break;
                    case IMAGETYPE_PNG:
                        $image = imagecreatefrompng($file);
                        break;
                    case IMAGETYPE_BMP:
                        $image = imagecreatefrombmp($file);
                        break;
                    case IMAGETYPE_GIF:
                        $image = imagecreatefromgif($file);
                        break;
                    default:
                        return false;
                }
                return $image;
            }
            
            $avatar = loadImage($randomAvatar);
            if ($avatar === false) {
                throw new Exception('无法加载头像图片');
            }
            
            // 设置头像大小和位置
            $avatarWidth = 255;
            $avatarHeight = 320;
            $avatarX = $tableX + 20; // 相对于表格的位置
            $avatarY = $tableY + 20; // 相对于表格的位置
            
            // 将头像绘制到背景图上
            imagecopyresampled($image, $avatar, $avatarX, $avatarY, 0, 0, $avatarWidth, $avatarHeight, imagesx($avatar), imagesy($avatar));
            imagedestroy($avatar);
            
            // 设置字体颜色
            $black = imagecolorallocate($image, 0, 0, 0);
            
            // 设置字体路径
            $font = __DIR__ . '/fonts/simhei.ttf';
            $fontSize = 16;
            
            // 在图片上写入基本信息（相对于表格的位置）
            imagettftext($image, $fontSize, 0, $tableX + 470, $tableY + 45, $black, $font, $name);
            imagettftext($image, $fontSize, 0, $tableX + 290, $tableY + 385, $black, $font, $idcard);
            imagettftext($image, $fontSize, 0, $tableX + 470, $tableY + 335, $black, $font, $fakeAddress);
            imagettftext($image, $fontSize, 0, $tableX + 470, $tableY + 285, $black, $font, $realAddress);
            imagettftext($image, $fontSize, 0, $tableX + 470, $tableY + 140, $black, $font, $birthdayCode);
            imagettftext($image, $fontSize, 0, $tableX + 470, $tableY + 90, $black, $font, $sex);
            imagettftext($image, $fontSize, 0, $tableX + 470, $tableY + 190, $black, $font, "中国");
            imagettftext($image, $fontSize, 0, $tableX + 1050, $tableY + 90, $black, $font, "汉");
            
            // 根据类型添加不同的文字信息
            if ($lx == '1') {
                // 起诉专用文字
                $lawsuitText = [
                    "兹证明：",
                    "1. 本证明仅用于民事诉讼起诉使用",
                    "2. 持证人信息真实有效",
                    "3. 本证明具有法律效力",
                    "4. 使用本证明需遵守相关法律法规",
                    "5. 伪造、变造本证明将依法追究法律责任",
                    "特此证明",
                    "中华人民共和国最高人民法院",
                    date('Y年m月d日')
                ];
                
                $y = $tableY + $tableHeight + 50; // 在表格下方50像素处开始
                foreach ($lawsuitText as $text) {
                    imagettftext($image, $fontSize, 0, $tableX + 50, $y, $black, $font, $text);
                    $y += 30;
                }
            } else {
                // 户籍信息专用文字
                $householdText = [
                    "户籍信息证明",
                    "1. 本证明仅用于户籍信息查询使用",
                    "2. 持证人信息真实有效",
                    "3. 本证明具有法律效力",
                    "4. 使用本证明需遵守相关法律法规",
                    "5. 伪造、变造本证明将依法追究法律责任",
                    "特此证明",
                    "中华人民共和国公安部",
                    date('Y年m月d日')
                ];
                
                $y = $tableY + $tableHeight + 50; // 在表格下方50像素处开始
                foreach ($householdText as $text) {
                    imagettftext($image, $fontSize, 0, $tableX + 50, $y, $black, $font, $text);
                    $y += 30;
                }
            }
            
            // 添加盖章图片
            $stamp = imagecreatefrompng('盖章.png');
            if ($stamp === false) {
                throw new Exception('无法加载盖章图片');
            }
            
            $stampWidth = imagesx($stamp);
            $stampHeight = imagesy($stamp);
            
            // 设置新的盖章大小（缩小到原来的50%）
            $newStampWidth = $stampWidth * 0.8;
            $newStampHeight = $stampHeight * 0.8;
            
            // 创建新的盖章图片
            $newStamp = imagecreatetruecolor($newStampWidth, $newStampHeight);
            imagealphablending($newStamp, false);
            imagesavealpha($newStamp, true);
            
            // 调整大小并保持透明度
            imagecopyresampled($newStamp, $stamp, 0, 0, 0, 0, $newStampWidth, $newStampHeight, $stampWidth, $stampHeight);
            
            // 自定义锐化效果（更强的锐化）
            $sharpness = 0.8; // 更小的值意味着更强的锐化效果
            $matrix = array(
                array(-1, -1, -1),
                array(-1, 8 + 1/$sharpness, -1),
                array(-1, -1, -1)
            );
            
            // 应用锐化矩阵
            imageconvolution($newStamp, $matrix, 1, 0);
            
            // 增加透明度
            imagefilter($newStamp, IMG_FILTER_COLORIZE, 0, 0, 0, 45);
            
            // 设置右下角基础位置（相对于表格的位置）
            $baseX = $tableX + 790;
            $baseY = $tableY + 210;
            
            // 生成随机偏移（-20到20像素之间）
            $offsetX = rand(-20, 20);
            $offsetY = rand(-20, 20);
            
            // 将盖章图片绘制到背景图上
            imagecopy($image, $newStamp, $baseX + $offsetX, $baseY + $offsetY, 0, 0, $newStampWidth, $newStampHeight);
            
            // 释放资源
            imagedestroy($stamp);
            imagedestroy($newStamp);
            
            // 保存图片
            $outputPath = 'output_' . time() . '.png';
            imagepng($image, $outputPath);
            imagedestroy($image);
            
            echo json_encode([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'name' => $name,
                    'idcard' => $idcard,
                    'fake_address' => $fakeAddress,
                    'real_address' => $realAddress,
                    'image_url' => "https://api.qnm6.top/api/dujia/".$outputPath
                ]
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            
        } catch (Exception $e) {
            echo json_encode([
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
        }
        break;
}

// 生成婚姻史信息的函数
function generateMarriageHistory($name, $idcard) {
    // 从身份证号中提取出生年份
    $birthYear = substr($idcard, 6, 4);
    $currentYear = date('Y');
    $age = $currentYear - $birthYear;
    
    // 根据年龄生成合理的婚姻史
    $marriages = [];
    $marriageCount = 0;
    
    if ($age >= 22) {
        // 第一段婚姻
        $firstMarriageYear = min($birthYear + 22 + rand(0, 5), $currentYear - 1);
        // 确保离婚时间至少比登记时间晚一年
        $firstDivorceYear = min($firstMarriageYear + 1 + rand(0, 9), $currentYear);
        
        $firstMarriageMonth = rand(1, 12);
        // 确保离婚月份在登记月份之后至少12个月
        $firstDivorceMonth = rand(1, 12);
        if ($firstMarriageYear == $firstDivorceYear - 1) {
            $firstDivorceMonth = max($firstMarriageMonth, $firstDivorceMonth);
        }
        
        $marriages[] = [
            '配偶姓名' => generateRandomName(),
            '登记时间' => $firstMarriageYear . '年' . $firstMarriageMonth . '月',
            '离婚时间' => $firstDivorceYear . '年' . $firstDivorceMonth . '月',
            '离婚原因' => ['感情不和', '性格不合', '经济纠纷', '家庭矛盾'][rand(0, 3)],
            '子女情况' => rand(0, 1) ? '育有一子' : '育有一女'
        ];
        $marriageCount++;
        
        // 如果年龄足够大，可能有多段婚姻
        if ($age >= 30 && rand(0, 1)) {
            // 确保第二次婚姻在第一次离婚后至少一年
            $secondMarriageYear = min($firstDivorceYear + 1 + rand(0, 2), $currentYear - 1);
            // 确保第二次离婚时间至少比第二次登记时间晚一年
            $secondDivorceYear = min($secondMarriageYear + 1 + rand(0, 7), $currentYear);
            
            $secondMarriageMonth = rand(1, 12);
            // 确保离婚月份在登记月份之后至少12个月
            $secondDivorceMonth = rand(1, 12);
            if ($secondMarriageYear == $secondDivorceYear - 1) {
                $secondDivorceMonth = max($secondMarriageMonth, $secondDivorceMonth);
            }
            
            $marriages[] = [
                '配偶姓名' => generateRandomName(),
                '登记时间' => $secondMarriageYear . '年' . $secondMarriageMonth . '月',
                '离婚时间' => $secondDivorceYear . '年' . $secondDivorceMonth . '月',
                '离婚原因' => ['感情不和', '性格不合', '经济纠纷', '家庭矛盾'][rand(0, 3)],
                '子女情况' => rand(0, 1) ? '育有一子' : '育有一女'
            ];
            $marriageCount++;
        }
    }
    
    return [
        '姓名' => $name,
        '身份证号' => $idcard,
        '婚姻状况' => $marriageCount > 0 ? '离异' : '未婚',
        '婚姻次数' => $marriageCount,
        '婚姻史' => $marriages
    ];
}

// 生成随机姓名的函数
function generateRandomName() {
    global $arrXing, $arrMing;
    $xing = $arrXing[array_rand($arrXing)];
    $ming = $arrMing[array_rand($arrMing)];
    return $xing . $ming;
}