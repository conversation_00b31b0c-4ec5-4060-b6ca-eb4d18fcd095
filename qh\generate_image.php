<?php
// 确保没有输出任何内容
ob_start();
ob_clean();
while (ob_get_level() > 1) {
    ob_end_clean();
}

// 设置响应头
header('Content-Type: image/png');
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Content-Disposition: inline; filename="family_info.png"');

// 获取POST参数
$name = isset($_POST['name']) ? trim($_POST['name']) : '';
$idcard = isset($_POST['idcard']) ? trim($_POST['idcard']) : '';

if (empty($name) || empty($idcard)) {
    header('Content-Type: text/plain; charset=utf-8');
    echo '姓名和身份证号不能为空';
    exit;
}

// 调用API获取家庭成员信息
$api_url = "http://" . $_SERVER['HTTP_HOST'] . "/family.php?name=" . urlencode($name) . "&idcard=" . urlencode($idcard);
$response = file_get_contents($api_url);
$data = json_decode($response, true);

if ($data['code'] !== 200) {
    header('Content-Type: text/plain; charset=utf-8');
    echo $data['message'];
    exit;
}

// 创建图片
$width = 1024;
$height = 768;
$image = imagecreatetruecolor($width, $height);

// 设置颜色
$white = imagecolorallocate($image, 255, 255, 255);
$black = imagecolorallocate($image, 0, 0, 0);
$red = imagecolorallocate($image, 200, 0, 0);
$gray = imagecolorallocate($image, 128, 128, 128);

// 填充背景
imagefill($image, 0, 0, $white);

// 绘制边框
imagerectangle($image, 10, 10, $width-11, $height-11, $black);
imagerectangle($image, 12, 12, $width-13, $height-13, $black);

// 设置字体
$font_paths = [
    __DIR__ . '/fonts/hw.ttf',  // 楷体
];

$font = null;
foreach ($font_paths as $font_path) {
    if (file_exists($font_path)) {
        $font = $font_path;
        break;
    }
}

if ($font === null) {
    error_log('No available font found');
    header('Content-Type: text/plain; charset=utf-8');
    echo '系统字体文件不存在，请联系管理员配置字体';
    exit;
}

// 绘制标题
$title = '家庭成员信息';
$title_size = 30;
$title_box = imagettfbbox($title_size, 0, $font, $title);
$title_width = abs($title_box[4] - $title_box[0]);
$title_x = ($width - $title_width) / 2;
imagettftext($image, $title_size, 0, $title_x, 80, $black, $font, $title);

// // 绘制红色印章
// $stamp_size = 120;
// $stamp_x = $width - 180;
// $stamp_y = 40;
// imagearc($image, $stamp_x, $stamp_y, $stamp_size, $stamp_size, 0, 360, $red);
// imagettftext($image, 16, 0, $stamp_x-40, $stamp_y+10, $red, $font, '家庭成员');
// imagettftext($image, 16, 90, $stamp_x-10, $stamp_y-30, $red, $font, '信息证明');

// 绘制成员信息
$y = 150;
$line_height = 25;
$label_x = 50;
$value_x = 180;

foreach ($data['data'] as $index => $member) {
    // 绘制分隔线和装饰
    if ($index > 0) {
        $y += 10;
        imageline($image, 30, $y-15, $width-31, $y-15, $gray);
        imageline($image, 30, $y-13, $width-31, $y-13, $gray);
    }

    // 绘制成员信息
    imagettftext($image, 16, 0, $label_x, $y, $black, $font, "姓名：");
    imagettftext($image, 16, 0, $value_x, $y, $black, $font, $member['name']);
    
    $y += $line_height;
    imagettftext($image, 16, 0, $label_x, $y, $black, $font, "身份证号：");
    imagettftext($image, 16, 0, $value_x, $y, $black, $font, $member['idcard']);
    
    $y += $line_height;
    imagettftext($image, 16, 0, $label_x, $y, $black, $font, "住址：");
    
    // 直接显示地址，不换行
    imagettftext($image, 16, 0, $value_x, $y, $black, $font, $member['address']);
    
    $y += $line_height + 15;
}

// 绘制生成时间
$date = date('Y年m月d日');
$time_text = "调档时间：{$date}";
$time_size = 14;
$time_box = imagettfbbox($time_size, 0, $font, $time_text);
$time_width = abs($time_box[4] - $time_box[0]);
$time_x = $width - $time_width - 50;
$time_y = $height - 50;
imagettftext($image, $time_size, 0, $time_x, $time_y, $gray, $font, $time_text);

// 输出图片
imagepng($image);
imagedestroy($image);