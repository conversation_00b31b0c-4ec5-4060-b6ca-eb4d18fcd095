<?php

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 获取动态参数
$msg = isset($_GET['msg']) ? $_GET['msg'] : '';
include '../../verify_vip.php';


// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = false;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}
// 记录执行时间
$startTime = microtime(true);

// 读取文件 wanghong.txt
$file = 'wanghong.txt';
$messages = [];
$found = false; // 标记是否找到匹配的行

if (file_exists($file)) {
    $lines = file($file, FILE_IGNORE_NEW_LINES); // 读取文件的每一行

    // 遍历文件内容，检查是否包含 msg
    foreach ($lines as $line) {
        if (strpos($line, $msg) !== false) {  // 如果行中包含 msg
            // 对每一行匹配的内容进行操作
            // 例如：将匹配的内容进行处理
            $messages[] = "{$line}";  // 示例操作：将匹配内容添加到 $messages 数组
            $found = true;  // 设置找到标志
        }
    }
}

// 计算执行时间
$executionTime = round(microtime(true) - $startTime, 4);

// 根据是否找到匹配项返回不同的 JSON
if ($found) {
    echo json_encode([
        'code' => 200,
        'message' => '查询成功',
        'shuju' => implode("\n\n", $messages),  // 将匹配结果用单个换行符连接
        'execution_time' => $executionTime . ' 秒'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} else {
    echo json_encode([
        'code' => 404,
        'message' => '库中无记录。',
        'execution_time' => $executionTime . ' 秒',
        '官方TG频道' => '官方TG频道@idatas8'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?>
