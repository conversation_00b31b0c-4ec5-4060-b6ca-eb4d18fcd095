# XXCY API 接口文档

## 文件结构
```
api/
├── xxcy/index.php          - 主要API文件
├── db.php                  - 数据库配置文件
├── verify_vip.php          - Token认证逻辑文件
└── api_logs/               - 日志目录
```

## 接口描述
XXCY API 是一个需要Token认证和手机号参数的接口。

## 请求地址
```
GET/POST /api/xxcy/
```

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | string | 是 | 用户认证Token |
| phone | string | 是 | 手机号码（11位数字，以1开头） |

## 请求示例
```
GET /api/xxcy/?token=your_token&phone=13800138000
```

或

```
POST /api/xxcy/
Content-Type: application/x-www-form-urlencoded

token=your_token&phone=13800138000
```

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "任务已提交，默认5分钟",
    "data": {
        "phone": "13800138000",
        "status": "submitted",
        "estimated_time": "5分钟",
        "submit_time": "2025-08-10 11:44:00"
    },
    "频道": "@nfgzs",
    "官网": "【及时更新,更多接口】https://api.qnm6.top/"
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "Token参数为必填项",
    "频道": "@nfgzs",
    "官网": "【及时更新,更多接口】https://api.qnm6.top/"
}
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 403 | 权限不足（非会员） |
| 404 | 用户被封禁 |
| 410 | 用户未注册 |
| 500 | 服务器内部错误 |

## 注意事项
1. 该接口需要会员权限才能使用
2. 手机号必须是有效的11位中国大陆手机号
3. Token必须是有效的用户认证令牌
4. 所有请求都会被记录到日志文件中
