// System Settings Page

class SettingsPage {
    constructor() {
        this.systemInfo = {};
        this.init();
    }

    init() {
        this.renderPage();
        this.loadSystemInfo();
    }

    renderPage() {
        const container = utils.$('#settings-page');
        if (!container) return;

        container.innerHTML = `
            <div class="settings-page">
                <div class="page-header">
                    <div class="page-header-content">
                        <h2>系统设置</h2>
                        <p>配置系统的基本信息和参数</p>
                    </div>
                    <div class="page-header-actions">
                        <button class="btn btn-primary" id="save-settings-btn">
                            <i class="fas fa-save"></i>
                            保存设置
                        </button>
                        <button class="btn btn-secondary" id="refresh-settings-btn">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <div class="settings-content">
                    <div class="settings-grid">
                        <!-- Basic Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-cog"></i>
                                    基本设置
                                </h3>
                            </div>
                            <div class="card-body">
                                <form id="basic-settings-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">软件版本号</label>
                                            <input type="text" id="appvid" class="form-control" placeholder="请输入版本号">
                                            <small class="form-text">当前软件的版本号</small>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">软件状态</label>
                                            <select id="appcode" class="form-control">
                                                <option value="200">正常运行</option>
                                                <option value="500">维护中</option>
                                                <option value="404">停止服务</option>
                                            </select>
                                            <small class="form-text">控制软件的运行状态</small>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">软件公告</label>
                                        <textarea id="appgg" class="form-control" rows="4" placeholder="请输入软件公告内容"></textarea>
                                        <small class="form-text">显示给用户的公告信息</small>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Download Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-download"></i>
                                    下载设置
                                </h3>
                            </div>
                            <div class="card-body">
                                <form id="download-settings-form">
                                    <div class="form-group">
                                        <label class="form-label">软件下载链接</label>
                                        <input type="url" id="appurl" class="form-control" placeholder="https://example.com/download">
                                        <small class="form-text">用户下载软件的链接</small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">软件背景图链接</label>
                                        <input type="url" id="appimg" class="form-control" placeholder="https://example.com/image.jpg">
                                        <small class="form-text">软件界面的背景图片</small>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Contact Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-address-book"></i>
                                    联系方式
                                </h3>
                            </div>
                            <div class="card-body">
                                <form id="contact-settings-form">
                                    <div class="form-group">
                                        <label class="form-label">官方QQ群号码</label>
                                        <input type="text" id="qqqid" class="form-control" placeholder="请输入QQ群号">
                                        <small class="form-text">用户交流的QQ群</small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">官网地址</label>
                                        <input type="url" id="gwurl" class="form-control" placeholder="https://example.com">
                                        <small class="form-text">官方网站地址</small>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Business Settings -->
                        <div class="settings-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-business-time"></i>
                                    商务设置
                                </h3>
                            </div>
                            <div class="card-body">
                                <form id="business-settings-form">
                                    <div class="form-group">
                                        <label class="form-label">代理申请网站链接</label>
                                        <input type="url" id="dailiurl" class="form-control" placeholder="https://example.com/agent">
                                        <small class="form-text">代理商申请页面</small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">广告跳转链接</label>
                                        <input type="url" id="guanggaourl" class="form-control" placeholder="https://example.com/ad">
                                        <small class="form-text">广告点击后的跳转地址</small>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- System Operations -->
                    <div class="system-operations">
                        <div class="operations-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-tools"></i>
                                    系统操作
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="operations-grid">
                                    <button class="operation-btn" id="update-launch-stats">
                                        <i class="fas fa-rocket"></i>
                                        <span>更新启动统计</span>
                                        <small>重置今日启动次数</small>
                                    </button>
                                    
                                    <button class="operation-btn" id="update-register-stats">
                                        <i class="fas fa-user-plus"></i>
                                        <span>更新注册统计</span>
                                        <small>重置今日注册数量</small>
                                    </button>
                                    
                                    <button class="operation-btn" id="update-vip-codes">
                                        <i class="fas fa-crown"></i>
                                        <span>更新VIP状态</span>
                                        <small>检查并更新过期VIP</small>
                                    </button>
                                    
                                    <button class="operation-btn" id="backup-system">
                                        <i class="fas fa-database"></i>
                                        <span>系统备份</span>
                                        <small>备份系统数据</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.bindEvents();
    }

    bindEvents() {
        // Save settings button
        const saveBtn = utils.$('#save-settings-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // Refresh button
        const refreshBtn = utils.$('#refresh-settings-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshSettings();
            });
        }

        // System operations
        const updateLaunchStatsBtn = utils.$('#update-launch-stats');
        if (updateLaunchStatsBtn) {
            updateLaunchStatsBtn.addEventListener('click', () => {
                this.updateLaunchStats();
            });
        }

        const updateRegisterStatsBtn = utils.$('#update-register-stats');
        if (updateRegisterStatsBtn) {
            updateRegisterStatsBtn.addEventListener('click', () => {
                this.updateRegisterStats();
            });
        }

        const updateVipCodesBtn = utils.$('#update-vip-codes');
        if (updateVipCodesBtn) {
            updateVipCodesBtn.addEventListener('click', () => {
                this.updateVipCodes();
            });
        }

        const backupSystemBtn = utils.$('#backup-system');
        if (backupSystemBtn) {
            backupSystemBtn.addEventListener('click', () => {
                this.backupSystem();
            });
        }

        // Form validation
        this.bindFormValidation();
    }

    bindFormValidation() {
        // URL validation
        const urlInputs = utils.$$('input[type="url"]');
        urlInputs.forEach(input => {
            input.addEventListener('blur', (e) => {
                const value = e.target.value.trim();
                if (value && !this.isValidUrl(value)) {
                    e.target.classList.add('is-invalid');
                    toast.warning('请输入有效的URL地址');
                } else {
                    e.target.classList.remove('is-invalid');
                }
            });
        });
    }

    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    async loadSystemInfo() {
        try {
            loading.show('加载系统设置...');
            
            const response = await api.getSystemInfo();
            loading.hide();

            if (response.code === 200) {
                this.systemInfo = response.msg;
                this.populateForm();
                toast.success('系统设置加载成功');
            } else {
                toast.error(response.message || '加载系统设置失败');
            }
        } catch (error) {
            loading.hide();
            console.error('加载系统设置失败:', error);
            toast.error('加载系统设置失败: ' + error.message);
        }
    }

    populateForm() {
        const fields = ['appvid', 'appcode', 'appgg', 'appurl', 'appimg', 'dailiurl', 'qqqid', 'guanggaourl', 'gwurl'];
        
        fields.forEach(field => {
            const element = utils.$(`#${field}`);
            if (element && this.systemInfo[field] !== undefined) {
                element.value = this.systemInfo[field] || '';
            }
        });
    }

    async saveSettings() {
        try {
            // Validate form
            const urlInputs = utils.$$('input[type="url"]');
            let hasErrors = false;
            
            urlInputs.forEach(input => {
                const value = input.value.trim();
                if (value && !this.isValidUrl(value)) {
                    input.classList.add('is-invalid');
                    hasErrors = true;
                }
            });

            if (hasErrors) {
                toast.error('请修正表单中的错误');
                return;
            }

            // Collect form data
            const formData = {
                appvid: utils.$('#appvid').value.trim(),
                appcode: utils.$('#appcode').value,
                appgg: utils.$('#appgg').value.trim(),
                appurl: utils.$('#appurl').value.trim(),
                appimg: utils.$('#appimg').value.trim(),
                dailiurl: utils.$('#dailiurl').value.trim(),
                qqqid: utils.$('#qqqid').value.trim(),
                guanggaourl: utils.$('#guanggaourl').value.trim(),
                gwurl: utils.$('#gwurl').value.trim()
            };

            loading.show('保存设置中...');
            const response = await api.updateSystemInfo(formData);
            loading.hide();

            if (response.code === 200) {
                this.systemInfo = response.msg;
                toast.success('设置保存成功');
            } else {
                toast.error(response.message || '保存设置失败');
            }
        } catch (error) {
            loading.hide();
            console.error('保存设置失败:', error);
            toast.error('保存设置失败: ' + error.message);
        }
    }

    async updateLaunchStats() {
        const confirmed = await modal.confirm('确定要更新启动统计吗？这将重置今日启动次数。', '更新启动统计');
        if (!confirmed) return;

        try {
            loading.show('更新启动统计中...');
            const response = await api.updateLaunchStats();
            loading.hide();

            if (response.code === 200) {
                toast.success('启动统计更新成功');
            } else {
                toast.error(response.message || '更新启动统计失败');
            }
        } catch (error) {
            loading.hide();
            console.error('更新启动统计失败:', error);
            toast.error('更新启动统计失败: ' + error.message);
        }
    }

    async updateRegisterStats() {
        const confirmed = await modal.confirm('确定要更新注册统计吗？这将重新计算注册数量。', '更新注册统计');
        if (!confirmed) return;

        try {
            loading.show('更新注册统计中...');
            const response = await api.updateRegisterStats();
            loading.hide();

            if (response.code === 200) {
                toast.success('注册统计更新成功');
            } else {
                toast.error(response.message || '更新注册统计失败');
            }
        } catch (error) {
            loading.hide();
            console.error('更新注册统计失败:', error);
            toast.error('更新注册统计失败: ' + error.message);
        }
    }

    async updateVipCodes() {
        const confirmed = await modal.confirm('确定要更新VIP状态吗？这将检查并更新所有过期的VIP用户。', '更新VIP状态');
        if (!confirmed) return;

        try {
            loading.show('更新VIP状态中...');
            const response = await api.updateVipCodes();
            loading.hide();

            if (response.code === 200) {
                const message = response.updated_users > 0 
                    ? `成功更新了 ${response.updated_users} 个过期VIP用户`
                    : '没有需要更新的VIP用户';
                toast.success(message);
            } else {
                toast.error(response.message || '更新VIP状态失败');
            }
        } catch (error) {
            loading.hide();
            console.error('更新VIP状态失败:', error);
            toast.error('更新VIP状态失败: ' + error.message);
        }
    }

    async backupSystem() {
        toast.info('系统备份功能正在开发中...');
    }

    async refreshSettings() {
        const refreshBtn = utils.$('#refresh-settings-btn');
        if (refreshBtn) {
            refreshBtn.classList.add('spinning');
        }

        try {
            await this.loadSystemInfo();
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('spinning');
            }
        }
    }

    refresh() {
        this.loadSystemInfo();
    }
}

// Export for global access
window.SettingsPage = SettingsPage;
