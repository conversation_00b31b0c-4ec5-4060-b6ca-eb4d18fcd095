<?php
/**
 * 今日统计API
 */

require_once '../db.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $today = date('Y-m-d');
    
    // 获取今日启动次数
    $todayLaunches = 0;
    $logFile = "../api_logs/api_log_{$today}.txt";
    
    if (file_exists($logFile)) {
        $content = file_get_contents($logFile);
        $todayLaunches = substr_count($content, 'API: demo');
    }
    
    // 获取今日注册数量
    $todayRegisters = 0;
    if (isset($mysqli) && $mysqli) {
        $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM users WHERE DATE(time) = ?");
        if ($stmt) {
            $stmt->bind_param("s", $today);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $todayRegisters = intval($row['count']);
            $stmt->close();
        }
    }
    
    // 获取总启动次数
    $totalLaunches = 0;
    $logDir = "../api_logs/";
    if (is_dir($logDir)) {
        $files = glob($logDir . "api_log_*.txt");
        foreach ($files as $file) {
            $content = file_get_contents($file);
            $totalLaunches += substr_count($content, 'API: demo');
        }
    }
    
    // 获取用户统计
    $totalUsers = 0;
    $vipUsers = 0;
    $bannedUsers = 0;
    
    if (isset($mysqli) && $mysqli) {
        $result = $mysqli->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            $totalUsers = intval($row['count']);
        }
        
        $result = $mysqli->query("SELECT COUNT(*) as count FROM users WHERE vipcode = '1'");
        if ($result) {
            $row = $result->fetch_assoc();
            $vipUsers = intval($row['count']);
        }
        
        $result = $mysqli->query("SELECT COUNT(*) as count FROM users WHERE tokencode = '500'");
        if ($result) {
            $row = $result->fetch_assoc();
            $bannedUsers = intval($row['count']);
        }
    }
    
    // 获取卡密统计
    $totalCards = 0;
    $usedCards = 0;
    
    if (isset($mysqli) && $mysqli) {
        $result = $mysqli->query("SELECT COUNT(*) as count FROM kamis");
        if ($result) {
            $row = $result->fetch_assoc();
            $totalCards = intval($row['count']);
        }
        
        $result = $mysqli->query("SELECT COUNT(*) as count FROM kamis WHERE kamicode = '1'");
        if ($result) {
            $row = $result->fetch_assoc();
            $usedCards = intval($row['count']);
        }
    }
    
    $response = [
        'code' => 200,
        'message' => 'success',
        'data' => [
            'today' => [
                'launches' => $todayLaunches,
                'registers' => $todayRegisters,
                'date' => $today
            ],
            'total' => [
                'launches' => $totalLaunches,
                'users' => $totalUsers,
                'vip_users' => $vipUsers,
                'banned_users' => $bannedUsers,
                'cards' => $totalCards,
                'used_cards' => $usedCards,
                'unused_cards' => $totalCards - $usedCards
            ]
        ]
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'code' => 500,
        'message' => '获取统计数据失败: ' . $e->getMessage()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>
