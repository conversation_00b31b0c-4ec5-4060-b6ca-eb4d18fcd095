#!/bin/bash

# 检查PID文件是否存在
if [ ! -f bot.pid ]; then
    echo "未找到PID文件，机器人可能未在运行。"
    exit 1
fi

# 读取PID
pid=$(cat bot.pid)

# 检查进程是否存在
if ! ps -p $pid > /dev/null; then
    echo "进程 $pid 不存在，机器人可能已经停止。"
    rm bot.pid
    exit 1
fi

# 终止进程
echo "正在停止机器人（PID: $pid）..."
kill $pid

# 等待进程结束
for i in {1..10}; do
    if ! ps -p $pid > /dev/null; then
        echo "机器人已成功停止！"
        rm bot.pid
        exit 0
    fi
    sleep 1
done

# 如果进程仍然存在，强制终止
if ps -p $pid > /dev/null; then
    echo "正在强制终止进程..."
    kill -9 $pid
    rm bot.pid
    echo "机器人已被强制停止！"
fi 