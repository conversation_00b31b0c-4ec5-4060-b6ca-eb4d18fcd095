<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VIP-短信测压 - iDatas社工API</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/css/common.css">
  <link rel="stylesheet" href="/css/ios18.css">
</head>
<body>
  <!-- 公共顶栏 -->
  <div id="header-include"></div>
  <script>
    fetch('/components/header.html').then(r=>r.text()).then(html=>{
      document.getElementById('header-include').innerHTML = html;
    });
  </script>
  <header class="bg-primary text-white text-center py-5 mb-4 ios18-header">
    <div class="container">
      <h1 class="display-5 fw-bold">VIP-短信测压 <span class="badge bg-warning text-dark ms-1">火热上新</span></h1>
      <p class="lead">传递手机号，执行短信测压任务</p>
    </div>
  </header>
  <main class="container mb-5">
    <section class="mb-5">
      <h2 class="h4 mb-3">接口详情</h2>
      <div class="ios18-card card p-4 mb-4">
        <div class="mb-2"><strong>接口地址：</strong><span class="text-monospace">http://api.qnm6.top/api/xxcy</span></div>
        <div class="mb-2"><strong>请求方法：</strong>GET / POST</div>
        <div class="mb-2"><strong>返回格式：</strong>JSON</div>
      </div>
      <h3 class="h5 mt-4 mb-2">请求参数说明</h3>
      <div class="ios18-card card p-4 mb-4">
        <div class="table-responsive">
          <table class="table table-bordered align-middle text-center mb-0">
            <thead class="table-light">
              <tr><th>名称</th><th>必填</th><th>类型</th><th>说明</th></tr>
            </thead>
            <tbody>
              <tr><td>phone</td><td>是</td><td>string</td><td>需要测压的手机号，如：13800138000</td></tr>
              <tr><td>token</td><td>是</td><td>string</td><td>用户鉴权token</td></tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="mb-4">
        <strong>调用示例：</strong>
        <div class="ios18-card card p-3 mt-2 bg-light position-relative">
          <code id="xxcy-demo">xxcy?phone=要测压的手机号&amp;token=你的Token</code>
          <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="xxcy-demo">拷贝</button>
        </div>
      </div>
      <h3 class="h5 mt-4 mb-2">返回示例</h3>
      <div class="ios18-card card p-3 bg-light mb-3 position-relative">
        <pre id="xxcy-resp" class="mb-0" style="white-space:pre-wrap;">{
  "code": 200,
  "message": "任务已提交，默认5分钟",
  "data": {
    "phone": "13800138000",
    "status": "submitted",
    "estimated_time": "5分钟",
    "submit_time": "2025-08-10 11:44:00"
  },
  "频道": "@nfgzs",
  "官网": "【及时更新,更多接口】https://api.qnm6.top/"
}</pre>
        <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="xxcy-resp">拷贝</button>
      </div>
      <h3 class="h5 mt-4 mb-2">返回参数说明</h3>
      <div class="ios18-card card p-4 mb-4">
        <div class="table-responsive">
          <table class="table table-bordered align-middle text-center mb-0">
            <thead class="table-light">
              <tr><th>名称</th><th>类型</th><th>说明</th></tr>
            </thead>
            <tbody>
              <tr><td>code</td><td>int</td><td>状态码，200为成功，其他为失败</td></tr>
              <tr><td>message</td><td>string</td><td>提示信息</td></tr>
              <tr><td>data</td><td>object</td><td>返回数据对象</td></tr>
              <tr><td>data.phone</td><td>string</td><td>目标手机号</td></tr>
              <tr><td>data.status</td><td>string</td><td>任务状态，submitted表示已提交</td></tr>
              <tr><td>data.estimated_time</td><td>string</td><td>预计执行时间</td></tr>
              <tr><td>data.submit_time</td><td>string</td><td>任务提交时间</td></tr>
              <tr><td>频道</td><td>string</td><td>官方频道信息</td></tr>
              <tr><td>官网</td><td>string</td><td>官方网站信息</td></tr>
            </tbody>
          </table>
        </div>
      </div>
      <h3 class="h5 mt-4 mb-2">错误码说明</h3>
      <div class="ios18-card card p-4 mb-4">
        <div class="table-responsive">
          <table class="table table-bordered align-middle text-center mb-0">
            <thead class="table-light">
              <tr><th>错误码</th><th>说明</th></tr>
            </thead>
            <tbody>
              <tr><td>200</td><td>成功</td></tr>
              <tr><td>400</td><td>参数错误（Token或手机号为空/格式错误）</td></tr>
              <tr><td>403</td><td>权限不足（非会员用户）</td></tr>
              <tr><td>404</td><td>用户被封禁</td></tr>
              <tr><td>410</td><td>用户未注册</td></tr>
              <tr><td>500</td><td>服务器内部错误</td></tr>
            </tbody>
          </table>
        </div>
      </div>
      <h3 class="h5 mt-4 mb-2">代码示例</h3>
      <div class="ios18-card card p-3 bg-light position-relative">
        <pre id="xxcy-code" class="mb-0" style="white-space:pre-wrap;">// GET请求示例
fetch('http://api.qnm6.top/api/xxcy?phone=13800138000&token=xxxxxxx')
  .then(res => res.json())
  .then(data => console.log(data));

// POST请求示例
fetch('http://api.qnm6.top/api/xxcy', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: 'phone=13800138000&token=xxxxxxx'
})
  .then(res => res.json())
  .then(data => console.log(data));
</pre>
        <button class="btn btn-sm btn-outline-primary ios18-copy-btn position-absolute top-0 end-0 m-2" data-copy-target="xxcy-code">拷贝</button>
      </div>
      <div class="alert alert-warning mt-4" role="alert">
        <h6 class="alert-heading"><i class="bi bi-exclamation-triangle-fill"></i> 重要提示</h6>
        <ul class="mb-0">
          <li>该接口需要会员权限才能使用</li>
          <li>手机号必须是有效的11位中国大陆手机号</li>
          <li>请合理使用，避免滥用</li>
          <li>所有请求都会被记录到日志文件中</li>
        </ul>
      </div>
    </section>
  </main>
  <footer class="text-center py-4 mt-5 border-top text-muted bg-light">
    <small>© 2023-2025 iDatas社工API;官方频道@idatas8</small>
  </footer>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/js/common.js"></script>
</body>
</html>
