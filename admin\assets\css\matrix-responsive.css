/* Matrix Responsive CSS */

/* Large Desktop - Matrix Theme */
@media (min-width: 1400px) {
    [data-theme="matrix"] .dashboard-grid::before {
        background: 
            radial-gradient(circle at 15% 40%, rgba(0, 255, 65, 0.04) 0%, transparent 50%),
            radial-gradient(circle at 85% 15%, rgba(0, 255, 65, 0.04) 0%, transparent 50%),
            radial-gradient(circle at 35% 85%, rgba(0, 255, 65, 0.04) 0%, transparent 50%),
            radial-gradient(circle at 65% 60%, rgba(0, 255, 65, 0.04) 0%, transparent 50%);
    }
    
    [data-theme="matrix"] .stat-card {
        border: 2px solid var(--matrix-border);
    }
    
    [data-theme="matrix"] .stat-card:hover {
        border-color: var(--matrix-green);
        box-shadow: 
            0 0 30px rgba(0, 255, 65, 0.3),
            inset 0 0 20px rgba(0, 255, 65, 0.1);
    }
}

/* Desktop - Matrix Theme */
@media (max-width: 1200px) {
    [data-theme="matrix"] .sidebar {
        width: 250px;
    }
    
    [data-theme="matrix"] .logo {
        font-size: 18px;
    }
    
    [data-theme="matrix"] .nav-link {
        padding: 10px 14px;
        font-size: 14px;
    }
    
    [data-theme="matrix"] .page-title {
        font-size: 22px;
        letter-spacing: 1.5px;
    }
}

/* Tablet - Matrix Theme */
@media (max-width: 992px) and (min-width: 769px) {
    [data-theme="matrix"] .sidebar {
        width: 70px;
    }
    
    [data-theme="matrix"] .sidebar .logo-text,
    [data-theme="matrix"] .sidebar .nav-link span {
        display: none;
    }
    
    [data-theme="matrix"] .logo {
        justify-content: center;
    }
    
    [data-theme="matrix"] .nav-link {
        justify-content: center;
        padding: 12px;
    }
    
    [data-theme="matrix"] .nav-link i {
        font-size: 20px;
    }
    
    [data-theme="matrix"] .main-content {
        margin-left: 70px;
    }
    
    [data-theme="matrix"] .page-title {
        font-size: 20px;
        letter-spacing: 1px;
    }
    
    [data-theme="matrix"] .stat-card {
        padding: 20px;
    }
    
    [data-theme="matrix"] .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    [data-theme="matrix"] .stat-content h3 {
        font-size: 24px;
    }
    
    [data-theme="matrix"] .dashboard-grid::before {
        background: 
            radial-gradient(circle at 25% 50%, rgba(0, 255, 65, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 75% 25%, rgba(0, 255, 65, 0.02) 0%, transparent 50%);
    }
}

/* Mobile Large - Matrix Theme */
@media (max-width: 768px) {
    [data-theme="matrix"] .sidebar {
        width: 280px;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 999;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        border-right: 2px solid var(--matrix-green);
        box-shadow: 0 0 30px rgba(0, 255, 65, 0.5);
    }
    
    [data-theme="matrix"] .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    [data-theme="matrix"] .sidebar .logo-text,
    [data-theme="matrix"] .sidebar .nav-link span {
        display: inline;
    }
    
    [data-theme="matrix"] .logo {
        justify-content: flex-start;
    }
    
    [data-theme="matrix"] .nav-link {
        justify-content: flex-start;
        padding: 12px 16px;
    }
    
    [data-theme="matrix"] .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        position: relative;
    }
    
    [data-theme="matrix"] .mobile-menu-toggle {
        display: block;
        color: var(--matrix-green);
        border: 1px solid var(--matrix-border);
        background: rgba(0, 255, 65, 0.1);
    }
    
    [data-theme="matrix"] .mobile-menu-toggle:hover {
        background: rgba(0, 255, 65, 0.2);
        box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
    }
    
    [data-theme="matrix"] .header {
        padding: 0 20px;
        border-bottom: 2px solid var(--matrix-border);
    }
    
    [data-theme="matrix"] .page-title {
        font-size: 18px;
        letter-spacing: 1px;
    }
    
    [data-theme="matrix"] .content-area {
        padding: 16px;
    }
    
    [data-theme="matrix"] .stat-card {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    [data-theme="matrix"] .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    [data-theme="matrix"] .stat-content h3 {
        font-size: 20px;
    }
    
    [data-theme="matrix"] .action-btn {
        padding: 12px 16px;
        font-size: 13px;
        letter-spacing: 0.5px;
    }
    
    [data-theme="matrix"] .chart-card .card-header,
    [data-theme="matrix"] .chart-card .card-body {
        padding: 16px;
    }
    
    [data-theme="matrix"] .quick-actions {
        padding: 16px;
    }
    
    [data-theme="matrix"] .quick-actions h3 {
        font-size: 16px;
        margin-bottom: 16px;
    }
    
    [data-theme="matrix"] .table-container {
        overflow-x: auto;
    }
    
    [data-theme="matrix"] .data-table {
        min-width: 600px;
        font-size: 12px;
    }
    
    [data-theme="matrix"] .data-table th,
    [data-theme="matrix"] .data-table td {
        padding: 10px 12px;
    }
    
    [data-theme="matrix"] .modal {
        width: 95%;
        margin: 20px;
    }
    
    [data-theme="matrix"] .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }
    
    [data-theme="matrix"] .toast {
        min-width: auto;
        width: 100%;
        font-size: 13px;
    }
    
    [data-theme="matrix"] .user-menu-dropdown {
        right: -10px;
        left: auto;
        min-width: 160px;
        border: 1px solid var(--matrix-green);
        box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    }
    
    [data-theme="matrix"] .header-actions {
        gap: 8px;
    }
    
    [data-theme="matrix"] .theme-toggle,
    [data-theme="matrix"] .refresh-btn {
        padding: 8px;
        font-size: 14px;
    }
    
    [data-theme="matrix"] .user-menu-toggle span {
        display: none;
    }
    
    [data-theme="matrix"] .dashboard-grid::before {
        background: 
            radial-gradient(circle at 30% 70%, rgba(0, 255, 65, 0.02) 0%, transparent 50%);
    }
}

/* Mobile Small - Matrix Theme */
@media (max-width: 480px) {
    [data-theme="matrix"] .sidebar {
        width: 280px;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 999;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        border-right: 2px solid var(--matrix-green);
        box-shadow: 0 0 30px rgba(0, 255, 65, 0.5);
    }

    [data-theme="matrix"] .sidebar.mobile-open {
        transform: translateX(0);
    }

    [data-theme="matrix"] .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        position: relative;
    }

    [data-theme="matrix"] .header {
        padding: 0 16px;
    }
    
    [data-theme="matrix"] .page-title {
        font-size: 16px;
        letter-spacing: 0.5px;
    }
    
    [data-theme="matrix"] .content-area {
        padding: 12px;
    }
    
    [data-theme="matrix"] .stat-card {
        padding: 14px;
        flex-direction: row;
        text-align: left;
        gap: 12px;
    }
    
    [data-theme="matrix"] .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    [data-theme="matrix"] .stat-content h3 {
        font-size: 18px;
    }
    
    [data-theme="matrix"] .stat-content p {
        font-size: 12px;
    }
    
    [data-theme="matrix"] .action-btn {
        padding: 10px 12px;
        font-size: 12px;
        letter-spacing: 0.5px;
    }
    
    [data-theme="matrix"] .chart-card .card-header,
    [data-theme="matrix"] .chart-card .card-body {
        padding: 12px 16px;
    }
    
    [data-theme="matrix"] .quick-actions {
        padding: 14px;
    }
    
    [data-theme="matrix"] .quick-actions h3 {
        font-size: 14px;
        margin-bottom: 14px;
    }
    
    [data-theme="matrix"] .table-header {
        padding: 14px 16px;
    }
    
    [data-theme="matrix"] .table-title {
        font-size: 14px;
    }
    
    [data-theme="matrix"] .data-table th,
    [data-theme="matrix"] .data-table td {
        padding: 8px 10px;
        font-size: 11px;
    }
    
    [data-theme="matrix"] .btn {
        padding: 8px 14px;
        font-size: 12px;
    }
    
    [data-theme="matrix"] .btn-sm {
        padding: 6px 10px;
        font-size: 10px;
    }
    
    [data-theme="matrix"] .form-control {
        padding: 10px 12px;
        font-size: 12px;
    }
    
    [data-theme="matrix"] .form-label {
        font-size: 11px;
    }
    
    [data-theme="matrix"] .modal-header .modal-title {
        font-size: 14px;
    }
    
    [data-theme="matrix"] .toast {
        padding: 10px 14px;
        font-size: 12px;
    }
    
    [data-theme="matrix"] .toast-title {
        font-size: 13px;
    }
    
    [data-theme="matrix"] .toast-message {
        font-size: 11px;
    }
    
    [data-theme="matrix"] .user-token,
    [data-theme="matrix"] .card-code,
    [data-theme="matrix"] .log-time,
    [data-theme="matrix"] .ip-address,
    [data-theme="matrix"] .id-card {
        font-size: 10px;
        padding: 2px 4px;
    }
    
    [data-theme="matrix"] .activity-item {
        padding: 8px 10px;
        gap: 8px;
    }
    
    [data-theme="matrix"] .activity-icon {
        width: 28px;
        height: 28px;
        font-size: 11px;
    }
    
    [data-theme="matrix"] .activity-text {
        font-size: 12px;
    }
    
    [data-theme="matrix"] .activity-meta {
        font-size: 10px;
        gap: 6px;
    }
    
    [data-theme="matrix"] .dashboard-grid::before {
        display: none;
    }
}

/* Matrix Mobile Overlay */
@media (max-width: 768px) {
    [data-theme="matrix"] .sidebar-overlay {
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(2px);
    }
    
    [data-theme="matrix"] .sidebar-overlay.active {
        background: rgba(0, 0, 0, 0.9);
    }
}

/* Matrix Print Styles */
@media print {
    [data-theme="matrix"] .chart-card,
    [data-theme="matrix"] .stat-card,
    [data-theme="matrix"] .table-container {
        border: 2px solid #000 !important;
        box-shadow: none !important;
    }
    
    [data-theme="matrix"] .stat-card::before {
        background: #000 !important;
    }
    
    [data-theme="matrix"] .page-title,
    [data-theme="matrix"] .card-header h3,
    [data-theme="matrix"] .table-title {
        color: #000 !important;
        text-shadow: none !important;
    }
    
    [data-theme="matrix"] .data-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    [data-theme="matrix"] .data-table td {
        color: #333 !important;
    }
}

/* Matrix High Contrast Mode */
@media (prefers-contrast: high) {
    [data-theme="matrix"] {
        --matrix-green: #00ff00;
        --matrix-text-green: #00ff00;
        --matrix-text-dim: #00cc00;
        --matrix-border: #00ff00;
        --matrix-bg-dark: #000000;
        --matrix-bg-darker: #000000;
    }
    
    [data-theme="matrix"] .nav-link:hover,
    [data-theme="matrix"] .btn:hover {
        outline: 2px solid var(--matrix-green);
    }
}

/* Matrix Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    [data-theme="matrix"] * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    [data-theme="matrix"] .dashboard-grid::before {
        display: none !important;
    }
    
    [data-theme="matrix"] .stat-card::after {
        display: none !important;
    }
    
    [data-theme="matrix"] .chart-card::before {
        display: none !important;
    }
    
    [data-theme="matrix"] .quick-actions h3::before {
        animation: none !important;
    }
    
    [data-theme="matrix"] .status-active::before {
        animation: none !important;
    }
}
