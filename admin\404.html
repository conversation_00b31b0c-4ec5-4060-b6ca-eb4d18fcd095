<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 20px;
        }

        .error-container {
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .error-code {
            font-size: 120px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .error-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .error-message {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .error-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .error-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .error-btn.primary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }

        .error-btn.primary:hover {
            background: white;
            color: #5a67d8;
        }

        .error-animation {
            margin-bottom: 40px;
            font-size: 80px;
            opacity: 0.7;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        .error-details {
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-details h3 {
            font-size: 18px;
            margin-bottom: 15px;
        }

        .error-details ul {
            list-style: none;
            text-align: left;
        }

        .error-details li {
            padding: 8px 0;
            opacity: 0.9;
        }

        .error-details li i {
            margin-right: 10px;
            color: rgba(255, 255, 255, 0.7);
        }

        @media (max-width: 768px) {
            .error-code {
                font-size: 80px;
            }
            
            .error-title {
                font-size: 24px;
            }
            
            .error-message {
                font-size: 16px;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .error-animation {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-animation">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-code">404</div>
        
        <h1 class="error-title">页面未找到</h1>
        
        <p class="error-message">
            抱歉，您访问的页面不存在或已被移动。<br>
            请检查URL是否正确，或返回首页继续浏览。
        </p>
        
        <div class="error-actions">
            <a href="demo.html" class="error-btn primary">
                <i class="fas fa-home"></i>
                返回首页
            </a>
            <a href="login.html" class="error-btn">
                <i class="fas fa-sign-in-alt"></i>
                管理登录
            </a>
            <a href="javascript:history.back()" class="error-btn">
                <i class="fas fa-arrow-left"></i>
                返回上页
            </a>
        </div>
        
        <div class="error-details">
            <h3>可能的原因：</h3>
            <ul>
                <li><i class="fas fa-link"></i>URL 地址输入错误</li>
                <li><i class="fas fa-file"></i>页面已被删除或移动</li>
                <li><i class="fas fa-server"></i>服务器配置问题</li>
                <li><i class="fas fa-clock"></i>页面正在维护中</li>
            </ul>
        </div>
    </div>

    <script>
        // 自动跳转倒计时（可选）
        let countdown = 10;
        const countdownElement = document.createElement('p');
        countdownElement.style.marginTop = '20px';
        countdownElement.style.opacity = '0.7';
        countdownElement.style.fontSize = '14px';
        
        function updateCountdown() {
            if (countdown > 0) {
                countdownElement.textContent = `${countdown} 秒后自动返回首页`;
                countdown--;
                setTimeout(updateCountdown, 1000);
            } else {
                window.location.href = 'demo.html';
            }
        }
        
        // 如果用户想要自动跳转，可以取消注释下面的代码
        // document.querySelector('.error-container').appendChild(countdownElement);
        // updateCountdown();
        
        // 记录 404 错误（用于分析）
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_not_found', {
                'page_location': window.location.href,
                'page_referrer': document.referrer
            });
        }
    </script>
</body>
</html>
