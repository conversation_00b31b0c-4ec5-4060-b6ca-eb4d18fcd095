import os

log_dir = 'api_logs'
# 要搜索的特定字符串
target_string = '3290wha46tzpz9frod'

lines_found = []

for root, dirs, files in os.walk(log_dir):
    for file in files:
        file_path = os.path.join(root, file)
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    # 检查行是否包含目标字符串
                    if target_string in line:
                        lines_found.append(f"{file_path}:{line_num} - {line.strip()}")
        except Exception as e:
            pass  # 忽略无法读取的文件

with open('sjh.txt', 'w', encoding='utf-8') as out:
    for line in lines_found:
        out.write(line + '\n')
