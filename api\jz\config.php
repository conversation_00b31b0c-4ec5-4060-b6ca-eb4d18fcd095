<?php
// 数据库配置
$dbConfig = [
    'host' => 'localhost',
    'dbname' => 'sjhdw11',
    'username' => 'sjhdw11',
    'password' => 'sjhdw11',
    'charset' => 'utf8mb4'
];

// 创建数据库连接
try {
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
} 