// API Service Layer

class ApiService {
    constructor() {
        this.baseUrl = '../admapi/';
        this.timeout = 10000; // 10 seconds
    }

    // Generic request method
    async request(endpoint, options = {}) {
        const {
            method = 'GET',
            params = {},
            data = null,
            timeout = this.timeout
        } = options;

        try {
            let url = this.baseUrl + endpoint;
            
            // Add query parameters for GET requests
            if (method === 'GET' && Object.keys(params).length > 0) {
                const searchParams = new URLSearchParams(params);
                url += '?' + searchParams.toString();
            }

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            const fetchOptions = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                signal: controller.signal
            };

            // Add body for POST requests
            if (method === 'POST' && data) {
                if (data instanceof FormData) {
                    delete fetchOptions.headers['Content-Type'];
                    fetchOptions.body = data;
                } else {
                    fetchOptions.body = JSON.stringify(data);
                }
            }

            const response = await fetch(url, fetchOptions);
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return result;

        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请稍后重试');
            }
            throw error;
        }
    }

    // User Management APIs
    async getUsers() {
        return this.request('users.php');
    }

    async banUser(token, code) {
        return this.request('ban.php', {
            params: { token, code }
        });
    }

    async rechargeUser(token, time) {
        return this.request('cz.php', {
            params: { token, time }
        });
    }

    async registerUser(token) {
        return this.request('zhuce.php', {
            params: { token }
        });
    }

    // Card Management APIs
    async getCards(code) {
        return this.request('kamis.php', {
            params: { code }
        });
    }

    async generateCards(time, count) {
        return this.request('sckami.php', {
            params: { time, s: count }
        });
    }

    async useCard(token, kami) {
        return this.request('sykami.php', {
            params: { token, kami }
        });
    }

    // System Settings APIs
    async getSystemInfo() {
        return this.request('msghq.php');
    }

    async updateSystemInfo(data) {
        const params = new URLSearchParams();
        Object.entries(data).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                params.append(key, value);
            }
        });
        
        return this.request('msgxg.php?' + params.toString());
    }

    // Statistics APIs
    async updateLaunchStats() {
        return this.request('qdltongji.php');
    }

    async updateRegisterStats() {
        return this.request('zctongji.php');
    }

    async updateVipCodes() {
        return this.request('vipcodes.php');
    }

    async getDemoData(token) {
        return this.request('demo.php', {
            params: { token }
        });
    }

    // Enhanced Log Management APIs
    async getLogs(date = null) {
        try {
            const logDate = date || new Date().toISOString().split('T')[0];
            return await this.getLogStats(logDate, 'api');
        } catch (error) {
            console.error('获取日志失败:', error);
            return { logs: [], message: '获取日志失败: ' + error.message };
        }
    }

    async getDemoLogs(date = null) {
        try {
            const logDate = date || new Date().toISOString().split('T')[0];
            return await this.getLogStats(logDate, 'demo');
        } catch (error) {
            console.error('获取Demo日志失败:', error);
            return { logs: [], message: '获取Demo日志失败: ' + error.message };
        }
    }

    async getDetailedLogStats(date = null) {
        try {
            const logDate = date || new Date().toISOString().split('T')[0];
            return await this.getLogStats(logDate, 'stats');
        } catch (error) {
            console.error('获取详细日志统计失败:', error);
            return { stats: null, message: '获取详细日志统计失败: ' + error.message };
        }
    }

    // Parse regular log file
    parseLogFile(text) {
        const lines = text.trim().split('\n').filter(line => line.trim());
        return lines.map(line => {
            try {
                // Parse log format: [timestamp] IP: ip1, ip2, Token: token, API: api, Params: params
                const match = line.match(/^\[([^\]]+)\]\s+IP:\s+([^,]+(?:,\s*[^,]+)*),\s+Token:\s+([^,]+),\s+API:\s+([^,]+),\s+Params:\s+(.+)$/);
                
                if (match) {
                    const [, timestamp, ips, token, api, paramsStr] = match;
                    let params = {};
                    
                    try {
                        params = JSON.parse(paramsStr);
                    } catch (e) {
                        params = { raw: paramsStr };
                    }
                    
                    return {
                        timestamp,
                        ips: ips.split(',').map(ip => ip.trim()),
                        token,
                        api,
                        params,
                        raw: line
                    };
                }
                
                return {
                    timestamp: '',
                    ips: [],
                    token: '',
                    api: '',
                    params: {},
                    raw: line
                };
            } catch (error) {
                return {
                    timestamp: '',
                    ips: [],
                    token: '',
                    api: '',
                    params: {},
                    raw: line,
                    error: error.message
                };
            }
        });
    }

    // Parse demo log file
    parseDemoLogFile(text) {
        const lines = text.trim().split('\n').filter(line => line.trim());
        return lines.map(line => {
            try {
                const data = JSON.parse(line);
                return {
                    timestamp: data.timestamp,
                    cardKey: data.card_key,
                    name: data.name,
                    idcard: data.idcard,
                    type: data.lx,
                    result: data.result,
                    remainingUses: data.result?.remaining_uses,
                    raw: line
                };
            } catch (error) {
                return {
                    timestamp: '',
                    cardKey: '',
                    name: '',
                    idcard: '',
                    type: '',
                    result: null,
                    remainingUses: null,
                    raw: line,
                    error: error.message
                };
            }
        });
    }

    // Get available log dates
    async getAvailableLogDates() {
        try {
            // This would need a server-side endpoint to list available log files
            // For now, we'll generate a list of recent dates
            const dates = [];
            const today = new Date();
            
            for (let i = 0; i < 30; i++) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                dates.push(date.toISOString().split('T')[0]);
            }
            
            return dates;
        } catch (error) {
            console.error('获取可用日志日期失败:', error);
            return [];
        }
    }

    // New enhanced APIs
    async getTodayStats() {
        return this.request('todaystats.php');
    }

    async getCardStats(status = 'all') {
        return this.request('cardstats.php', {
            params: { status }
        });
    }

    async getUserStats(type = 'all') {
        return this.request('userstats.php', {
            params: { type }
        });
    }

    async getLogStats(date = null, type = 'api') {
        const params = { type };
        if (date) params.date = date;
        return this.request('logstats.php', {
            params
        });
    }

    // Enhanced dashboard statistics
    async getDashboardStats() {
        try {
            const [todayStatsResponse, userStatsResponse, cardStatsResponse] = await Promise.all([
                this.getTodayStats(),
                this.getUserStats('stats'),
                this.getCardStats('stats')
            ]);

            const todayStats = todayStatsResponse.data || {};
            const userStats = userStatsResponse.stats || {};
            const cardStats = cardStatsResponse.stats || {};

            return {
                totalUsers: todayStats.total?.users || 0,
                vipUsers: todayStats.total?.vip_users || 0,
                bannedUsers: todayStats.total?.banned_users || 0,
                todayLaunches: todayStats.today?.launches || 0,
                todayRegisters: todayStats.today?.registers || 0,
                totalLaunches: todayStats.total?.launches || 0,
                totalCards: todayStats.total?.cards || 0,
                usedCards: todayStats.total?.used_cards || 0,
                unusedCards: todayStats.total?.unused_cards || 0,
                recentUsers: userStats.recent_users || [],
                dailyRegisters: userStats.daily_registers || [],
                cardsByDays: cardStats.by_days || []
            };
        } catch (error) {
            console.error('获取仪表盘统计失败:', error);
            return {
                totalUsers: 0,
                vipUsers: 0,
                bannedUsers: 0,
                todayLaunches: 0,
                todayRegisters: 0,
                totalLaunches: 0,
                totalCards: 0,
                usedCards: 0,
                unusedCards: 0,
                recentUsers: [],
                dailyRegisters: [],
                cardsByDays: []
            };
        }
    }
}

// Create global API instance
window.api = new ApiService();
