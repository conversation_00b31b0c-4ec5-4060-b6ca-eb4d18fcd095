// Matrix Effects JavaScript

class MatrixEffects {
    constructor() {
        this.enabled = false;
        this.canvas = null;
        this.ctx = null;
        this.characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
        this.fontSize = 14;
        this.columns = 0;
        this.drops = [];
        this.animationId = null;
        
        this.init();
    }

    init() {
        this.createCanvas();
        this.setupCanvas();
        this.bindEvents();
    }

    createCanvas() {
        this.canvas = document.createElement('canvas');
        this.canvas.id = 'matrix-canvas';
        this.canvas.style.position = 'fixed';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.pointerEvents = 'none';
        this.canvas.style.zIndex = '-1';
        this.canvas.style.opacity = '0';
        this.canvas.style.transition = 'opacity 0.5s ease';
        
        document.body.appendChild(this.canvas);
        this.ctx = this.canvas.getContext('2d');
    }

    setupCanvas() {
        this.resizeCanvas();
        this.initDrops();
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        
        this.columns = Math.floor(this.canvas.width / this.fontSize);
        this.initDrops();
    }

    initDrops() {
        this.drops = [];
        for (let i = 0; i < this.columns; i++) {
            this.drops[i] = Math.random() * this.canvas.height;
        }
    }

    bindEvents() {
        window.addEventListener('resize', () => {
            this.resizeCanvas();
        });

        // Listen for theme changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    const theme = document.documentElement.getAttribute('data-theme');
                    if (theme === 'matrix' && !this.enabled) {
                        this.enable();
                    } else if (theme !== 'matrix' && this.enabled) {
                        this.disable();
                    }
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
    }

    enable() {
        if (this.enabled) return;
        
        this.enabled = true;
        this.canvas.style.opacity = '0.1';
        this.startAnimation();
    }

    disable() {
        if (!this.enabled) return;
        
        this.enabled = false;
        this.canvas.style.opacity = '0';
        this.stopAnimation();
    }

    toggleEffects() {
        if (this.enabled) {
            this.disable();
        } else {
            this.enable();
        }
    }

    startAnimation() {
        if (this.animationId) return;
        
        const animate = () => {
            if (!this.enabled) return;
            
            this.draw();
            this.animationId = requestAnimationFrame(animate);
        };
        
        animate();
    }

    stopAnimation() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    draw() {
        // Create fade effect
        this.ctx.fillStyle = 'rgba(1, 4, 9, 0.05)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Set text properties
        this.ctx.fillStyle = '#00ff41';
        this.ctx.font = `${this.fontSize}px 'Courier New', monospace`;

        // Draw characters
        for (let i = 0; i < this.drops.length; i++) {
            const char = this.characters[Math.floor(Math.random() * this.characters.length)];
            const x = i * this.fontSize;
            const y = this.drops[i] * this.fontSize;

            // Add glow effect
            this.ctx.shadowColor = '#00ff41';
            this.ctx.shadowBlur = 10;
            this.ctx.fillText(char, x, y);
            this.ctx.shadowBlur = 0;

            // Reset drop position when it reaches bottom
            if (y > this.canvas.height && Math.random() > 0.975) {
                this.drops[i] = 0;
            }

            // Move drop down
            this.drops[i]++;
        }
    }

    // Matrix rain effect for specific elements
    addMatrixRain(element) {
        if (!element) return;

        element.style.position = 'relative';
        element.style.overflow = 'hidden';

        const rain = document.createElement('div');
        rain.className = 'matrix-rain';
        rain.style.position = 'absolute';
        rain.style.top = '0';
        rain.style.left = '0';
        rain.style.width = '100%';
        rain.style.height = '100%';
        rain.style.pointerEvents = 'none';
        rain.style.zIndex = '1';
        rain.style.opacity = '0.1';

        // Create falling characters
        for (let i = 0; i < 20; i++) {
            const char = document.createElement('span');
            char.textContent = this.characters[Math.floor(Math.random() * this.characters.length)];
            char.style.position = 'absolute';
            char.style.color = '#00ff41';
            char.style.fontSize = '12px';
            char.style.fontFamily = 'Courier New, monospace';
            char.style.left = Math.random() * 100 + '%';
            char.style.animationDuration = (Math.random() * 3 + 2) + 's';
            char.style.animationDelay = Math.random() * 2 + 's';
            char.style.animationName = 'matrixFall';
            char.style.animationIterationCount = 'infinite';
            char.style.animationTimingFunction = 'linear';

            rain.appendChild(char);
        }

        element.appendChild(rain);

        // Add CSS animation if not exists
        if (!document.querySelector('#matrix-fall-style')) {
            const style = document.createElement('style');
            style.id = 'matrix-fall-style';
            style.textContent = `
                @keyframes matrixFall {
                    0% {
                        transform: translateY(-100%);
                        opacity: 0;
                    }
                    10% {
                        opacity: 1;
                    }
                    90% {
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(100vh);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Matrix typing effect
    addTypingEffect(element, text, speed = 100) {
        if (!element || !text) return;

        element.textContent = '';
        let i = 0;

        const typeChar = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(typeChar, speed + Math.random() * 50);
            }
        };

        typeChar();
    }

    // Matrix glitch effect
    addGlitchEffect(element) {
        if (!element) return;

        const originalText = element.textContent;
        const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        
        let glitchInterval;
        
        const startGlitch = () => {
            glitchInterval = setInterval(() => {
                let glitchedText = '';
                for (let i = 0; i < originalText.length; i++) {
                    if (Math.random() < 0.1) {
                        glitchedText += glitchChars[Math.floor(Math.random() * glitchChars.length)];
                    } else {
                        glitchedText += originalText[i];
                    }
                }
                element.textContent = glitchedText;
            }, 50);

            setTimeout(() => {
                clearInterval(glitchInterval);
                element.textContent = originalText;
            }, 200);
        };

        element.addEventListener('mouseenter', startGlitch);
    }

    // Initialize matrix effects for theme
    initMatrixTheme() {
        const theme = document.documentElement.getAttribute('data-theme');
        if (theme === 'matrix') {
            this.enable();
            
            // Add effects to specific elements
            const pageTitle = document.querySelector('.page-title');
            if (pageTitle) {
                this.addGlitchEffect(pageTitle);
            }

            const logo = document.querySelector('.logo');
            if (logo) {
                this.addGlitchEffect(logo);
            }

            // Add matrix rain to dashboard
            const dashboard = document.querySelector('#dashboard-page');
            if (dashboard) {
                this.addMatrixRain(dashboard);
            }
        }
    }

    // Clean up effects
    cleanup() {
        this.disable();
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }
        
        // Remove matrix rain elements
        const rainElements = document.querySelectorAll('.matrix-rain');
        rainElements.forEach(el => el.remove());
        
        // Remove style
        const style = document.querySelector('#matrix-fall-style');
        if (style) {
            style.remove();
        }
    }
}

// Initialize matrix effects
document.addEventListener('DOMContentLoaded', () => {
    window.matrixEffects = new MatrixEffects();
    
    // Auto-enable if matrix theme is active
    setTimeout(() => {
        window.matrixEffects.initMatrixTheme();
    }, 1000);
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    if (window.matrixEffects) {
        window.matrixEffects.cleanup();
    }
});
