<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
include __DIR__ . '/config.php';

// 记录上传请求日志
function logUpload($message, $data = []) {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $logFile = $logDir . '/upload_debug_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    $logEntry = sprintf(
        "[%s] %s | IP: %s | Data: %s\n",
        $timestamp,
        $message,
        $ip,
        json_encode($data)
    );

    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否是POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 记录请求开始
logUpload('UPLOAD_START', [
    'method' => $_SERVER['REQUEST_METHOD'],
    'post_data' => $_POST,
    'files' => array_keys($_FILES)
]);

// 获取参数并进行安全验证
$spaceId = trim($_POST['space_id'] ?? '');

logUpload('SPACE_ID_CHECK', ['space_id' => $spaceId, 'length' => strlen($spaceId)]);

// 验证space_id格式
if (!isValidSpaceId($spaceId)) {
    logUpload('SPACE_ID_INVALID', ['space_id' => $spaceId]);
    http_response_code(400);
    echo json_encode(['error' => 'Invalid space_id']);
    exit;
}

// 检查文件上传并验证文件类型
logUpload('FILE_CHECK', [
    'files_isset' => isset($_FILES['photo']),
    'file_error' => $_FILES['photo']['error'] ?? 'not_set',
    'upload_err_ok' => UPLOAD_ERR_OK
]);

if (!isset($_FILES['photo']) || $_FILES['photo']['error'] !== UPLOAD_ERR_OK) {
    logUpload('FILE_UPLOAD_ERROR', [
        'error_code' => $_FILES['photo']['error'] ?? 'not_set',
        'error_message' => 'No photo uploaded or upload error'
    ]);
    http_response_code(400);
    echo json_encode(['error' => 'No photo uploaded']);
    exit;
}

// 验证文件类型和大小
if (!isValidFileType($_FILES['photo']['type'], $_FILES['photo']['name'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid file type']);
    exit;
}

if ($_FILES['photo']['size'] > MAX_FILE_SIZE) {
    http_response_code(400);
    echo json_encode(['error' => 'File too large']);
    exit;
}

$dataDir = __DIR__ . '/data';
$imgDir = __DIR__ . '/../../tpimg';
$dataFile = $dataDir . '/' . $spaceId . '.json';

// 检查空间是否存在
if (!file_exists($dataFile)) {
    http_response_code(404);
    echo json_encode(['error' => 'Space not found']);
    exit;
}

// 读取空间数据
$spaceData = json_decode(file_get_contents($dataFile), true);
if (!$spaceData) {
    http_response_code(500);
    echo json_encode(['error' => 'Invalid space data']);
    exit;
}

// 生成安全的唯一文件名
$fileExtension = 'jpg';
$fileName = $spaceId . '_' . time() . '_' . bin2hex(random_bytes(8)) . '.' . $fileExtension;
$filePath = $imgDir . '/' . $fileName;

// 确保文件名不存在（避免冲突）
while (file_exists($filePath)) {
    $fileName = $spaceId . '_' . time() . '_' . bin2hex(random_bytes(8)) . '.' . $fileExtension;
    $filePath = $imgDir . '/' . $fileName;
}

// 移动上传的文件
logUpload('FILE_MOVE_ATTEMPT', [
    'tmp_name' => $_FILES['photo']['tmp_name'],
    'target_path' => $filePath,
    'target_dir_exists' => is_dir(dirname($filePath)),
    'target_dir_writable' => is_writable(dirname($filePath))
]);

if (move_uploaded_file($_FILES['photo']['tmp_name'], $filePath)) {
    logUpload('FILE_MOVE_SUCCESS', ['filename' => $fileName, 'path' => $filePath]);

    // 更新空间数据
    $spaceData['images'][] = $fileName;
    $spaceData['view_count']++;
    $spaceData['last_access'] = date('Y-m-d H:i:s');

    // 保存更新后的数据
    file_put_contents($dataFile, json_encode($spaceData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

    logUpload('UPLOAD_COMPLETE', ['filename' => $fileName]);
    echo json_encode(['success' => true, 'filename' => $fileName]);
} else {
    logUpload('FILE_MOVE_FAILED', [
        'tmp_name' => $_FILES['photo']['tmp_name'],
        'target_path' => $filePath,
        'tmp_exists' => file_exists($_FILES['photo']['tmp_name']),
        'target_dir_exists' => is_dir(dirname($filePath)),
        'target_dir_writable' => is_writable(dirname($filePath))
    ]);
    http_response_code(500);
    echo json_encode(['error' => 'Failed to save photo']);
}
?>
