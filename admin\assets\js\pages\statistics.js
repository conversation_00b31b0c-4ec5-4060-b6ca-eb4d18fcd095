// Statistics Page

class StatisticsPage {
    constructor() {
        this.charts = {};
        this.statsData = {};
        this.init();
    }

    init() {
        this.renderPage();
        this.loadStatistics();
    }

    renderPage() {
        const container = utils.$('#statistics-page');
        if (!container) return;

        container.innerHTML = `
            <div class="statistics-page">
                <div class="page-header">
                    <div class="page-header-content">
                        <h2>数据统计</h2>
                        <p>查看系统的各项数据统计和分析报告</p>
                    </div>
                    <div class="page-header-actions">
                        <button class="btn btn-primary" id="export-stats-btn">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                        <button class="btn btn-secondary" id="refresh-stats-btn">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <!-- Overview Stats -->
                <div class="stats-overview">
                    <div class="overview-card">
                        <div class="overview-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="overview-total-users">0</h3>
                            <p>总用户数</p>
                            <span class="overview-change" id="users-change">+0%</span>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="overview-total-launches">0</h3>
                            <p>总启动次数</p>
                            <span class="overview-change" id="launches-change">+0%</span>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="overview-total-cards">0</h3>
                            <p>总卡密数</p>
                            <span class="overview-change" id="cards-change">+0%</span>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="overview-content">
                            <h3 id="overview-vip-users">0</h3>
                            <p>VIP用户数</p>
                            <span class="overview-change" id="vip-change">+0%</span>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="charts-section">
                    <div class="charts-grid">
                        <!-- User Registration Trend -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>用户注册趋势</h3>
                                <div class="chart-controls">
                                    <select id="user-chart-period" class="form-control">
                                        <option value="7">最近7天</option>
                                        <option value="30">最近30天</option>
                                        <option value="90">最近90天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="userRegistrationChart"></canvas>
                            </div>
                        </div>

                        <!-- Launch Statistics -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>启动量统计</h3>
                                <div class="chart-controls">
                                    <select id="launch-chart-period" class="form-control">
                                        <option value="7">最近7天</option>
                                        <option value="30">最近30天</option>
                                        <option value="90">最近90天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="launchStatisticsChart"></canvas>
                            </div>
                        </div>

                        <!-- VIP Distribution -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>用户类型分布</h3>
                            </div>
                            <div class="chart-body">
                                <canvas id="userDistributionChart"></canvas>
                            </div>
                        </div>

                        <!-- Card Usage -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>卡密使用情况</h3>
                            </div>
                            <div class="chart-body">
                                <canvas id="cardUsageChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Statistics -->
                <div class="detailed-stats">
                    <div class="stats-table-container">
                        <div class="table-header">
                            <h3>详细统计数据</h3>
                        </div>
                        <div class="stats-table">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>新增用户</th>
                                        <th>启动次数</th>
                                        <th>新增VIP</th>
                                        <th>卡密使用</th>
                                        <th>活跃度</th>
                                    </tr>
                                </thead>
                                <tbody id="stats-table-body">
                                    <!-- Data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.bindEvents();
    }

    bindEvents() {
        // Export button
        const exportBtn = utils.$('#export-stats-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportStatistics();
            });
        }

        // Refresh button
        const refreshBtn = utils.$('#refresh-stats-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshStatistics();
            });
        }

        // Chart period selectors
        const userChartPeriod = utils.$('#user-chart-period');
        if (userChartPeriod) {
            userChartPeriod.addEventListener('change', () => {
                this.updateUserChart();
            });
        }

        const launchChartPeriod = utils.$('#launch-chart-period');
        if (launchChartPeriod) {
            launchChartPeriod.addEventListener('change', () => {
                this.updateLaunchChart();
            });
        }
    }

    async loadStatistics() {
        try {
            loading.show('加载统计数据...');

            // Load comprehensive statistics
            const [dashboardStats, userStats, cardStats, todayStats] = await Promise.all([
                api.getDashboardStats(),
                api.getUserStats('stats'),
                api.getCardStats('stats'),
                api.getTodayStats()
            ]);

            loading.hide();

            // Process data
            this.statsData = {
                dashboard: dashboardStats,
                users: userStats.stats || {},
                cards: cardStats.stats || {},
                today: todayStats.data || {},
                dailyRegisters: userStats.stats?.daily_registers || [],
                cardsByDays: cardStats.stats?.by_days || []
            };

            this.updateOverviewStats();
            this.initCharts();
            this.updateStatsTable();

            toast.success('统计数据加载成功');
        } catch (error) {
            loading.hide();
            console.error('加载统计数据失败:', error);
            toast.error('加载统计数据失败: ' + error.message);
        }
    }

    updateOverviewStats() {
        const { dashboard = {}, users = {}, cards = {}, today = {} } = this.statsData;

        const totalUsers = dashboard.totalUsers || users.total || 0;
        const vipUsers = dashboard.vipUsers || users.vip || 0;
        const totalCards = dashboard.totalCards || cards.total || 0;
        const totalLaunches = dashboard.totalLaunches || today.total?.launches || 0;

        // Update overview cards
        utils.$('#overview-total-users').textContent = utils.formatNumber(totalUsers);
        utils.$('#overview-vip-users').textContent = utils.formatNumber(vipUsers);
        utils.$('#overview-total-cards').textContent = utils.formatNumber(totalCards);
        utils.$('#overview-total-launches').textContent = utils.formatNumber(totalLaunches);

        // Calculate and display changes based on real data
        this.updateChangeIndicators();
    }

    updateChangeIndicators() {
        // Mock change data - in real app, this would be calculated from historical data
        const changes = [
            { id: 'users-change', value: 12.5 },
            { id: 'launches-change', value: -3.2 },
            { id: 'cards-change', value: 8.7 },
            { id: 'vip-change', value: 15.3 }
        ];

        changes.forEach(change => {
            const element = utils.$(`#${change.id}`);
            if (element) {
                const isPositive = change.value > 0;
                element.textContent = `${isPositive ? '+' : ''}${change.value}%`;
                element.className = `overview-change ${isPositive ? 'positive' : 'negative'}`;
            }
        });
    }

    initCharts() {
        this.initUserRegistrationChart();
        this.initLaunchStatisticsChart();
        this.initUserDistributionChart();
        this.initCardUsageChart();
    }

    initUserRegistrationChart() {
        const ctx = utils.$('#userRegistrationChart');
        if (!ctx) return;

        // Use real data from dailyRegisters
        const dailyRegisters = this.statsData.dailyRegisters || [];
        const labels = [];
        const data = [];

        // Fill in the last 7 days
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            const dayData = dailyRegisters.find(d => d.date === dateStr);

            labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
            data.push(dayData ? parseInt(dayData.count) : 0);
        }

        if (this.charts.userRegistration) {
            this.charts.userRegistration.destroy();
        }

        this.charts.userRegistration = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '新增用户',
                    data: data,
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                }
            }
        });
    }

    initLaunchStatisticsChart() {
        const ctx = utils.$('#launchStatisticsChart');
        if (!ctx) return;

        const labels = this.generateDateLabels(7);
        const data = this.generateSampleData(7, 50, 200);

        if (this.charts.launchStatistics) {
            this.charts.launchStatistics.destroy();
        }

        this.charts.launchStatistics = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '启动次数',
                    data: data,
                    backgroundColor: 'rgba(118, 75, 162, 0.8)',
                    borderColor: 'rgb(118, 75, 162)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                }
            }
        });
    }

    initUserDistributionChart() {
        const ctx = utils.$('#userDistributionChart');
        if (!ctx) return;

        const { users = {}, dashboard = {} } = this.statsData;
        const vipUsers = dashboard.vipUsers || users.vip || 0;
        const normalUsers = users.normal || 0;
        const bannedUsers = dashboard.bannedUsers || users.banned || 0;

        if (this.charts.userDistribution) {
            this.charts.userDistribution.destroy();
        }

        this.charts.userDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['VIP用户', '普通用户', '封禁用户'],
                datasets: [{
                    data: [vipUsers, normalUsers, bannedUsers],
                    backgroundColor: [
                        'rgb(255, 215, 0)',
                        'rgb(102, 126, 234)',
                        'rgb(245, 101, 101)'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    initCardUsageChart() {
        const ctx = utils.$('#cardUsageChart');
        if (!ctx) return;

        const { cards = {}, dashboard = {} } = this.statsData;
        const usedCards = dashboard.usedCards || cards.used || 0;
        const unusedCards = dashboard.unusedCards || cards.unused || 0;

        if (this.charts.cardUsage) {
            this.charts.cardUsage.destroy();
        }

        this.charts.cardUsage = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['已使用', '未使用'],
                datasets: [{
                    data: [usedCards, unusedCards],
                    backgroundColor: [
                        'rgb(72, 187, 120)',
                        'rgb(237, 137, 54)'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    updateUserChart() {
        const period = parseInt(utils.$('#user-chart-period').value);
        const labels = this.generateDateLabels(period);
        const data = this.generateSampleData(period, 5, 25);

        if (this.charts.userRegistration) {
            this.charts.userRegistration.data.labels = labels;
            this.charts.userRegistration.data.datasets[0].data = data;
            this.charts.userRegistration.update();
        }
    }

    updateLaunchChart() {
        const period = parseInt(utils.$('#launch-chart-period').value);
        const labels = this.generateDateLabels(period);
        const data = this.generateSampleData(period, 50, 200);

        if (this.charts.launchStatistics) {
            this.charts.launchStatistics.data.labels = labels;
            this.charts.launchStatistics.data.datasets[0].data = data;
            this.charts.launchStatistics.update();
        }
    }

    updateStatsTable() {
        const tbody = utils.$('#stats-table-body');
        if (!tbody) return;

        // Generate sample table data
        const tableData = this.generateTableData(7);
        
        tbody.innerHTML = tableData.map(row => `
            <tr>
                <td>${row.date}</td>
                <td>${row.newUsers}</td>
                <td>${row.launches}</td>
                <td>${row.newVips}</td>
                <td>${row.cardUsage}</td>
                <td>
                    <div class="activity-bar">
                        <div class="activity-fill" style="width: ${row.activity}%"></div>
                        <span>${row.activity}%</span>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    generateDateLabels(days) {
        const labels = [];
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
        }
        return labels;
    }

    generateSampleData(count, min, max) {
        return Array.from({ length: count }, () => 
            Math.floor(Math.random() * (max - min + 1)) + min
        );
    }

    generateTableData(days) {
        const data = [];
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            
            data.push({
                date: date.toLocaleDateString('zh-CN'),
                newUsers: Math.floor(Math.random() * 20) + 5,
                launches: Math.floor(Math.random() * 150) + 50,
                newVips: Math.floor(Math.random() * 5) + 1,
                cardUsage: Math.floor(Math.random() * 10) + 2,
                activity: Math.floor(Math.random() * 40) + 60
            });
        }
        return data;
    }

    exportStatistics() {
        // Generate CSV content
        const headers = ['日期', '新增用户', '启动次数', '新增VIP', '卡密使用', '活跃度'];
        const tableData = this.generateTableData(30); // Export 30 days of data
        
        const csvContent = [
            headers.join(','),
            ...tableData.map(row => [
                row.date,
                row.newUsers,
                row.launches,
                row.newVips,
                row.cardUsage,
                row.activity + '%'
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `statistics_report_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        toast.success('统计报告已导出');
    }

    async refreshStatistics() {
        const refreshBtn = utils.$('#refresh-stats-btn');
        if (refreshBtn) {
            refreshBtn.classList.add('spinning');
        }

        try {
            await this.loadStatistics();
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('spinning');
            }
        }
    }

    refresh() {
        this.loadStatistics();
    }

    destroy() {
        // Destroy all charts
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
    }
}

// Export for global access
window.StatisticsPage = StatisticsPage;
