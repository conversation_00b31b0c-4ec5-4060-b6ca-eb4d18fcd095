<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
// 获取动态参数
$token = isset($_GET['token']) ? trim($_GET['token']) : '';
$name = isset($_GET['name']) ? trim($_GET['name']) : '';
$idcard = isset($_GET['idcard']) ? trim($_GET['idcard']) : '';
$imgurl = isset($_GET['imgurl']) ? trim($_GET['imgurl']) : '';


include '../../verify_vip.php';


// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = true;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}



// 验证输入参数
if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,4}$/u', $name)) {
    echo json_encode(['code' => 400, 'message' => '姓名必须为2-4个字的纯中文。',
    '频道' => "@nfgzs",
    '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

if (!preg_match('/^\d{17}[\dXx]$/', $idcard)) {
    echo json_encode(['code' => 400, 'message' => '身份证号格式不正确，必须为18位数字或17位数字加X/x。',
    '频道' => "@nfgzs",
    '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// if (!filter_var($imgurl, FILTER_VALIDATE_URL) || !preg_match('/\.(jpg|png)$/i', $imgurl)) {
//     echo json_encode(['code' => 400, 'message' => '图片URL格式不正确，必须为jpg或png格式。',
//     '频道' => "@nfgzs",
//     '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
// ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
//     exit;
// }

$host = "https://selfiev2.market.alicloudapi.com";
$path = "/face/verify_selfie_idnumber";
$method = "POST";
$appcode = "4dd7d482bd05400cafea84de9e803e5a";  // 请替换为你的 AppCode
$headers = array();
array_push($headers, "Authorization:APPCODE " . $appcode);
// 修正 Content-Type 头部
array_push($headers, "Content-Type: application/x-www-form-urlencoded; charset=UTF-8");


// 构建请求体
$bodys = "id_number=" . urlencode($idcard) . "&name=" . $name . "&image_url=" . $imgurl . "&auto_rotate=true";
// "image" 传图片的 base64 编码数据，"image_url" 传图片的网络地址，二选一

$url = $host . $path;

// 初始化 cURL
$curl = curl_init();
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
curl_setopt($curl, CURLOPT_FAILONERROR, false);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_HEADER, true);
if (1 == strpos("$".$host, "https://")) {
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
}
curl_setopt($curl, CURLOPT_POSTFIELDS, $bodys);

// 获取返回结果
$response = curl_exec($curl);

// 检查是否有错误
if(curl_errno($curl)) {
    echo "cURL Error: " . curl_error($curl);
    exit;
}

// 记录 HTTP 状态码
$httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

// 分离 Header 和 Body
list($header, $body) = explode("\r\n\r\n", $response, 2);

// 清理 BOM 和多余的空格
$body = trim($body);
$body = mb_convert_encoding($body, 'UTF-8', 'UTF-8,GBK,GB2312,BIG5');

// 解析 JSON 数据
$data = json_decode($body, true);
curl_close($curl);

// 处理返回数据
if ($httpcode == 200) {
    // 如果接口返回状态为 OK
    if (isset($data['status']) && $data['status'] == "OK") {
        $result_code = $data['result_code'];  // 核验结果
        $score = isset($data['score']) ? (int)$data['score'] : 0;  // 比对分数

        // 根据 result_code 返回不同的判断
        if ($result_code == 1001) {
            $verification_result = '核验通过，身份和人脸一致';
        } elseif ($result_code == 1002) {
            $verification_result = '核验无法确认，身份和人脸匹配度低';
        } elseif ($result_code == 1003) {
            $verification_result = '核验失败，身份和人脸不匹配';
        } else {
            $verification_result = '核验结果不明';
        }

        // 返回成功响应
        echo json_encode([
            'code' => 200,
            'message' => '核验完成',
           'result' => $verification_result,
            'similarity' => round($score, 2) . '%', // 相似度百分比
            'execution_time' => round(microtime(true) - $_SERVER["REQUEST_TIME_FLOAT"], 4) . "s",
            '频道' => "@nfgzs",
            '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

    } else {
        // 返回接口错误信息
        echo json_encode([
            'code' => 400,
            'message' => '核验失败，未知错误',
            'execution_time' => round(microtime(true) - $_SERVER["REQUEST_TIME_FLOAT"], 4) . "s",
            '频道' => "@nfgzs",
            '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
} else {
    // 处理 HTTP 错误响应
    $errorMsg = isset($data['reason']) ? $data['reason'] : '未知错误';
    echo json_encode([
        'code' => $httpcode,
        'message' => "核验失败：$errorMsg",
        'execution_time' => round(microtime(true) - $_SERVER["REQUEST_TIME_FLOAT"], 4) . "s",
        '频道' => "@nfgzs",
        '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?>
