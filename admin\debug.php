<?php
// 简单的调试页面
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>API调试页面</h1>";

// 测试数据库连接
echo "<h2>数据库连接测试</h2>";
require_once '../db.php';

if (isset($mysqli) && $mysqli) {
    echo "✅ 数据库连接成功<br>";
    
    // 测试查询
    $result = $mysqli->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "✅ 用户总数: " . $row['count'] . "<br>";
    } else {
        echo "❌ 查询失败: " . $mysqli->error . "<br>";
    }
} else {
    echo "❌ 数据库连接失败<br>";
}

// 测试日志文件
echo "<h2>日志文件测试</h2>";
$today = date('Y-m-d');
$logFile = "../api_logs/api_log_{$today}.txt";

if (file_exists($logFile)) {
    echo "✅ 今日API日志文件存在<br>";
    $content = file_get_contents($logFile);
    $lines = explode("\n", $content);
    $demoCount = 0;
    foreach ($lines as $line) {
        if (strpos($line, 'API: demo') !== false) {
            $demoCount++;
        }
    }
    echo "✅ 今日demo调用次数: {$demoCount}<br>";
} else {
    echo "❌ 今日API日志文件不存在: {$logFile}<br>";
}

$demoLogFile = "../api_logs/demo_api_log_{$today}.txt";
if (file_exists($demoLogFile)) {
    echo "✅ 今日Demo日志文件存在<br>";
} else {
    echo "❌ 今日Demo日志文件不存在: {$demoLogFile}<br>";
}

// 测试API调用
echo "<h2>API调用测试</h2>";

// 测试todaystats.php
echo "<h3>todaystats.php</h3>";
$url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/../admapi/todaystats.php';
echo "调用URL: {$url}<br>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10
    ]
]);

$result = @file_get_contents($url, false, $context);
if ($result) {
    echo "✅ API调用成功<br>";
    echo "<pre>" . htmlspecialchars($result) . "</pre>";
} else {
    echo "❌ API调用失败<br>";
    $error = error_get_last();
    if ($error) {
        echo "错误信息: " . $error['message'] . "<br>";
    }
}

echo "<hr>";
echo "<a href='test.html'>返回测试页面</a>";
?>
