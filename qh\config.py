import os

# Telegram Bot配置
TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')

# API endpoints
FAMILY_API = os.getenv('FAMILY_API', 'http://149.88.95.125:987/family.php')
PHONE_API = os.getenv('PHONE_API', 'https://bothook.qnm6.top')
GH_API = os.getenv('GH_API', 'https://api.qnm6.top/api/gh1')
DA_API = os.getenv('DA_API', 'https://api.qnm6.top/api/gh2')
BG_API = os.getenv('BG_API', 'http://mika.qnm6.top:8080/mika.php')

# API Tokens
GH_TOKEN = os.getenv('GH_TOKEN', '143254321515132765')
DA_TOKEN = os.getenv('DA_TOKEN', '143254321515132765')

# 数据库配置
DB_FILE = os.getenv('DB_FILE', 'bot.db')

# 缓存配置
CACHE_DIR = os.getenv('CACHE_DIR', 'cache')

# 功能消耗积分配置
POINTS_CONFIG = {
    'qh': 12,
    'gh': 10,
    'da': 10,
    'jz': 8,
    'bg': 10
} 