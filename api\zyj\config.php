<?php
/**
 * 临时空间API配置文件
 * 包含所有安全设置和常量定义
 */

// 强制使用HTTPS
define('FORCE_HTTPS', true);

// 域名配置（如果需要固定域名）
define('API_DOMAIN', $_SERVER['HTTP_HOST']);

// 文件上传限制
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']);
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// 目录权限
define('DIR_PERMISSIONS', 0755);
define('FILE_PERMISSIONS', 0644);

// 安全设置
define('MAX_SPACES_PER_TOKEN', 10); // 每个Token最多创建10个空间
define('SPACE_EXPIRY_DAYS', 7); // 空间7天后过期
define('MAX_IMAGES_PER_SPACE', 50); // 每个空间最多50张图片

// 生成安全的URL
function getSecureUrl($path = '') {
    $protocol = FORCE_HTTPS ? 'https' : (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http');
    return $protocol . '://' . API_DOMAIN . $path;
}

// 验证文件类型
function isValidFileType($fileType, $fileName) {
    // 检查MIME类型
    if (!in_array($fileType, ALLOWED_FILE_TYPES)) {
        return false;
    }
    
    // 检查文件扩展名
    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if (!in_array($extension, ALLOWED_EXTENSIONS)) {
        return false;
    }
    
    return true;
}

// 清理过期空间
function cleanExpiredSpaces($dataDir) {
    $files = glob($dataDir . '/*.json');
    $expiredTime = time() - (SPACE_EXPIRY_DAYS * 24 * 60 * 60);
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data && isset($data['created_at'])) {
            $createdTime = strtotime($data['created_at']);
            if ($createdTime < $expiredTime) {
                // 删除过期的空间数据和图片
                if (isset($data['images'])) {
                    foreach ($data['images'] as $imgFile) {
                        $imgPath = __DIR__ . '/../../../tpimg/' . $imgFile;
                        if (file_exists($imgPath)) {
                            unlink($imgPath);
                        }
                    }
                }
                unlink($file);
            }
        }
    }
}

// 验证Token格式
function isValidToken($token) {
    // Token应该是字母数字组合，长度在8-64之间
    return preg_match('/^[a-zA-Z0-9]{8,64}$/', $token);
}

// 验证空间ID格式
function isValidSpaceId($spaceId) {
    // 空间ID应该是字母数字组合，长度在10-50之间
    return preg_match('/^[a-zA-Z0-9]{10,50}$/', $spaceId);
}

// 记录操作日志
function logOperation($operation, $token, $spaceId = '', $result = 'success') {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, DIR_PERMISSIONS, true);
    }
    
    $logFile = $logDir . '/operations_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $logEntry = sprintf(
        "[%s] %s | Token: %s | SpaceID: %s | IP: %s | UA: %s | Result: %s\n",
        $timestamp,
        $operation,
        substr($token, 0, 8) . '***', // 只记录Token前8位
        $spaceId,
        $ip,
        substr($userAgent, 0, 100), // 限制UA长度
        $result
    );
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 检查请求频率限制
function checkRateLimit($token, $operation) {
    $cacheDir = __DIR__ . '/cache';
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, DIR_PERMISSIONS, true);
    }
    
    $cacheFile = $cacheDir . '/rate_' . md5($token . $operation) . '.txt';
    $currentTime = time();
    $timeWindow = 60; // 1分钟时间窗口
    $maxRequests = 10; // 每分钟最多10次请求
    
    if (file_exists($cacheFile)) {
        $data = json_decode(file_get_contents($cacheFile), true);
        if ($data && isset($data['requests'], $data['window_start'])) {
            if ($currentTime - $data['window_start'] < $timeWindow) {
                if ($data['requests'] >= $maxRequests) {
                    return false; // 超过频率限制
                }
                $data['requests']++;
            } else {
                // 新的时间窗口
                $data = ['requests' => 1, 'window_start' => $currentTime];
            }
        } else {
            $data = ['requests' => 1, 'window_start' => $currentTime];
        }
    } else {
        $data = ['requests' => 1, 'window_start' => $currentTime];
    }
    
    file_put_contents($cacheFile, json_encode($data), LOCK_EX);
    return true;
}
?>
