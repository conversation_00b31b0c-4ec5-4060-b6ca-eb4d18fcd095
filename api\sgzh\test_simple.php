<?php
// 简单测试文件，验证修复后的功能
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 修复后的增强版API测试</h1>";

// 引入修复后的文件
include_once 'index.php';

echo "<h2>1. 系统状态检查</h2>";
echo "<p><strong>专业包状态:</strong> " . ($useAdvancedLibs ? "✅ 已加载" : "❌ 未加载，使用简化版本") . "</p>";

if ($useAdvancedLibs) {
    echo "<p style='color:green;'>✅ 系统将使用专业的身份证验证和手机号归属地查询包</p>";
} else {
    echo "<p style='color:orange;'>⚠️ 系统将使用内置的简化版本</p>";
    echo "<p><small>如需更准确的数据，请安装专业包：<br>";
    echo "composer require jxlwqq/id-validator<br>";
    echo "composer require shitoudev/phone-location</small></p>";
}

echo "<h2>2. 身份证信息提取测试</h2>";

$testIdCards = [
    "110101199001011234", // 北京
    "******************", // 广东深圳
    "310101198001011234"  // 上海
];

foreach ($testIdCards as $testIdCard) {
    echo "<h3>🆔 测试身份证: $testIdCard</h3>";
    
    $idCardInfo = extractIdCardInfo($testIdCard);
    
    if (!empty($idCardInfo)) {
        echo "<div style='background:#f0f8ff; padding:15px; margin:10px 0; border-radius:8px;'>";
        foreach ($idCardInfo as $key => $value) {
            echo "<p><strong>$key:</strong> $value</p>";
        }
        echo "</div>";
    } else {
        echo "<p style='color:red;'>❌ 无法提取信息</p>";
    }
    
    echo "<hr>";
}

echo "<h2>3. 手机号信息提取测试</h2>";

$testPhones = [
    "13812345678", // 移动
    "18621281566", // 联通
    "15912345678"  // 移动
];

foreach ($testPhones as $testPhone) {
    echo "<h3>📱 测试手机号: $testPhone</h3>";
    
    $phoneInfo = getPhoneLocation($testPhone);
    
    if (!empty($phoneInfo)) {
        echo "<div style='background:#f0fff0; padding:15px; margin:10px 0; border-radius:8px;'>";
        foreach ($phoneInfo as $key => $value) {
            echo "<p><strong>$key:</strong> $value</p>";
        }
        echo "</div>";
    } else {
        echo "<p style='color:red;'>❌ 无法提取信息</p>";
    }
    
    echo "<hr>";
}

echo "<h2>4. API调用测试</h2>";
echo "<p>您可以通过以下方式测试完整的API：</p>";
echo "<code>GET /extend/api/sgzh/index.php?msg=110101199001011234&token=your_token</code>";

echo "<h2>5. 功能说明</h2>";
echo "<ul>";
echo "<li>✅ 修复了vendor路径问题</li>";
echo "<li>✅ 支持专业包和简化版本的智能切换</li>";
echo "<li>✅ 增强了错误处理机制</li>";
echo "<li>✅ 扩展了手机号段支持</li>";
echo "<li>✅ 添加了数据来源标识</li>";
echo "</ul>";

?>

<style>
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}
h1, h2, h3 {
    color: #333;
}
code {
    background: #f4f4f4;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}
hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}
</style>
