// Users Management Page

class UsersPage {
    constructor() {
        this.users = [];
        this.dataTable = null;
        this.init();
    }

    init() {
        this.renderPage();
        this.loadUsers();
    }

    renderPage() {
        const container = utils.$('#users-page');
        if (!container) return;

        container.innerHTML = `
            <div class="users-page">
                <div class="page-header">
                    <div class="page-header-content">
                        <h2>用户管理</h2>
                        <p>管理系统中的所有用户，包括封禁、解封、充值等操作</p>
                    </div>
                    <div class="page-header-actions">
                        <button class="btn btn-primary" id="add-user-btn">
                            <i class="fas fa-plus"></i>
                            添加用户
                        </button>
                        <button class="btn btn-secondary" id="refresh-users-btn">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <div class="users-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-users-count">0</h3>
                            <p>总用户数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="vip-users-count">0</h3>
                            <p>VIP用户</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="active-users-count">0</h3>
                            <p>正常用户</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-slash"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="banned-users-count">0</h3>
                            <p>封禁用户</p>
                        </div>
                    </div>
                </div>

                <div id="users-table-container"></div>
            </div>
        `;

        this.bindEvents();
        this.initDataTable();
    }

    bindEvents() {
        // Add user button
        const addUserBtn = utils.$('#add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                this.showAddUserModal();
            });
        }

        // Refresh button
        const refreshBtn = utils.$('#refresh-users-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshUsers();
            });
        }
    }

    initDataTable() {
        const container = utils.$('#users-table-container');
        if (!container) return;

        this.dataTable = new DataTable(container, {
            title: '用户列表',
            columns: [
                {
                    key: 'token',
                    title: '用户Token',
                    render: (value) => `<code class="user-token">${value}</code>`
                },
                {
                    key: 'time',
                    title: '注册时间',
                    render: (value) => utils.formatDate(value)
                },
                {
                    key: 'vipcode',
                    title: 'VIP状态',
                    render: (value, row) => {
                        if (value === '1') {
                            const vipTime = new Date(row.viptime);
                            const now = new Date();
                            const isExpired = vipTime < now;
                            
                            return `
                                <span class="status-badge ${isExpired ? 'status-inactive' : 'status-vip'}">
                                    ${isExpired ? 'VIP已过期' : 'VIP用户'}
                                </span>
                            `;
                        }
                        return '<span class="status-badge status-inactive">普通用户</span>';
                    }
                },
                {
                    key: 'viptime',
                    title: 'VIP到期时间',
                    render: (value) => {
                        if (!value) return '-';
                        const vipTime = new Date(value);
                        const now = new Date();
                        const isExpired = vipTime < now;
                        
                        return `
                            <span class="${isExpired ? 'text-danger' : 'text-success'}">
                                ${utils.formatDate(value)}
                            </span>
                        `;
                    }
                },
                {
                    key: 'tokencode',
                    title: '账户状态',
                    render: (value) => {
                        if (value === '500') {
                            return '<span class="status-badge status-inactive">已封禁</span>';
                        }
                        return '<span class="status-badge status-active">正常</span>';
                    }
                },
                {
                    key: 'actions',
                    title: '操作',
                    render: (value, row) => {
                        const isBanned = row.tokencode === '500';
                        const isVip = row.vipcode === '1';
                        
                        return `
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-primary user-recharge" data-token="${row.token}">
                                    <i class="fas fa-plus"></i>
                                    充值
                                </button>
                                <button class="btn btn-sm ${isBanned ? 'btn-success' : 'btn-warning'} user-ban" 
                                        data-token="${row.token}" data-banned="${isBanned}">
                                    <i class="fas ${isBanned ? 'fa-unlock' : 'fa-lock'}"></i>
                                    ${isBanned ? '解封' : '封禁'}
                                </button>
                                <button class="btn btn-sm btn-info user-details" data-token="${row.token}">
                                    <i class="fas fa-eye"></i>
                                    详情
                                </button>
                            </div>
                        `;
                    }
                }
            ],
            data: [],
            pagination: true,
            pageSize: 20,
            searchable: true,
            sortable: true,
            actions: [
                {
                    name: 'export',
                    text: '导出数据',
                    icon: 'fas fa-download',
                    className: 'btn-secondary',
                    onClick: () => this.exportUsers()
                }
            ]
        });

        // Bind table action events
        utils.delegate(utils.$('#users-table-container'), '.user-recharge', 'click', (e) => {
            const token = e.target.dataset.token;
            this.showRechargeModal(token);
        });

        utils.delegate(utils.$('#users-table-container'), '.user-ban', 'click', (e) => {
            const token = e.target.dataset.token;
            const isBanned = e.target.dataset.banned === 'true';
            this.toggleUserBan(token, isBanned);
        });

        utils.delegate(utils.$('#users-table-container'), '.user-details', 'click', (e) => {
            const token = e.target.dataset.token;
            this.showUserDetails(token);
        });
    }

    async loadUsers() {
        try {
            loading.show('加载用户数据...');

            const [usersResponse, statsResponse] = await Promise.all([
                api.getUsers(),
                api.getUserStats('stats')
            ]);

            loading.hide();

            if (usersResponse.code === 200) {
                this.users = usersResponse.users || [];
                this.userStats = statsResponse.stats || {};
                this.updateStats();
                this.dataTable.setData(this.users);
                toast.success('用户数据加载成功');
            } else {
                toast.error(usersResponse.message || '加载用户数据失败');
            }
        } catch (error) {
            loading.hide();
            console.error('加载用户数据失败:', error);
            toast.error('加载用户数据失败: ' + error.message);
        }
    }

    updateStats() {
        // Use stats from API if available, otherwise calculate from users array
        const stats = this.userStats || {};

        const totalUsers = stats.total || this.users.length;
        const vipUsers = stats.vip || this.users.filter(user => user.vipcode === '1').length;
        const activeUsers = stats.normal || this.users.filter(user => user.tokencode === '200' && user.vipcode === '0').length;
        const bannedUsers = stats.banned || this.users.filter(user => user.tokencode === '500').length;

        utils.$('#total-users-count').textContent = utils.formatNumber(totalUsers);
        utils.$('#vip-users-count').textContent = utils.formatNumber(vipUsers);
        utils.$('#active-users-count').textContent = utils.formatNumber(activeUsers);
        utils.$('#banned-users-count').textContent = utils.formatNumber(bannedUsers);
    }

    async showAddUserModal() {
        const token = await modal.prompt('请输入用户Token', '', '添加用户');
        
        if (!token) return;

        if (!utils.validate.required(token)) {
            toast.error('请输入有效的Token');
            return;
        }

        try {
            loading.show('正在添加用户...');
            const response = await api.registerUser(token);
            loading.hide();

            if (response.code === 200) {
                toast.success('用户添加成功');
                this.loadUsers(); // Refresh user list
            } else {
                toast.error(response.message || '添加用户失败');
            }
        } catch (error) {
            loading.hide();
            console.error('添加用户失败:', error);
            toast.error('添加用户失败: ' + error.message);
        }
    }

    async showRechargeModal(token) {
        const modalContent = `
            <div class="form-group">
                <label class="form-label">用户Token</label>
                <input type="text" class="form-control" value="${token}" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">充值天数</label>
                <input type="number" id="recharge-days" class="form-control" placeholder="请输入天数" min="1" value="30">
            </div>
        `;

        modal.show({
            title: '用户充值',
            content: modalContent,
            buttons: [
                {
                    text: '取消',
                    className: 'btn-secondary'
                },
                {
                    text: '充值',
                    className: 'btn-primary',
                    onClick: async () => {
                        const days = utils.$('#recharge-days').value;

                        if (!days || days < 1) {
                            toast.error('请输入有效的天数');
                            return false;
                        }

                        try {
                            loading.show('正在充值...');
                            const response = await api.rechargeUser(token, days);
                            loading.hide();

                            if (response.code === 200) {
                                toast.success('充值成功');
                                this.loadUsers(); // Refresh user list
                            } else {
                                toast.error(response.message || '充值失败');
                            }
                        } catch (error) {
                            loading.hide();
                            console.error('充值失败:', error);
                            toast.error('充值失败: ' + error.message);
                        }
                    }
                }
            ]
        });
    }

    async toggleUserBan(token, isBanned) {
        const action = isBanned ? '解封' : '封禁';
        const confirmed = await modal.confirm(`确定要${action}用户 ${token} 吗？`, `${action}用户`);
        
        if (!confirmed) return;

        try {
            loading.show(`正在${action}用户...`);
            const code = isBanned ? '0' : '1'; // 0 = normal, 1 = banned
            const response = await api.banUser(token, code);
            loading.hide();

            if (response.code === 200) {
                toast.success(`用户${action}成功`);
                this.loadUsers(); // Refresh user list
            } else {
                toast.error(response.message || `${action}失败`);
            }
        } catch (error) {
            loading.hide();
            console.error(`${action}用户失败:`, error);
            toast.error(`${action}用户失败: ` + error.message);
        }
    }

    showUserDetails(token) {
        const user = this.users.find(u => u.token === token);
        if (!user) {
            toast.error('用户不存在');
            return;
        }

        const vipStatus = user.vipcode === '1' ? 'VIP用户' : '普通用户';
        const accountStatus = user.tokencode === '500' ? '已封禁' : '正常';
        const vipTime = user.viptime ? utils.formatDate(user.viptime) : '无';

        const modalContent = `
            <div class="user-details">
                <div class="detail-item">
                    <label>用户Token:</label>
                    <span class="detail-value"><code>${user.token}</code></span>
                </div>
                <div class="detail-item">
                    <label>注册时间:</label>
                    <span class="detail-value">${utils.formatDate(user.time)}</span>
                </div>
                <div class="detail-item">
                    <label>VIP状态:</label>
                    <span class="detail-value">
                        <span class="status-badge ${user.vipcode === '1' ? 'status-vip' : 'status-inactive'}">
                            ${vipStatus}
                        </span>
                    </span>
                </div>
                <div class="detail-item">
                    <label>VIP到期时间:</label>
                    <span class="detail-value">${vipTime}</span>
                </div>
                <div class="detail-item">
                    <label>账户状态:</label>
                    <span class="detail-value">
                        <span class="status-badge ${user.tokencode === '500' ? 'status-inactive' : 'status-active'}">
                            ${accountStatus}
                        </span>
                    </span>
                </div>
            </div>
        `;

        modal.show({
            title: '用户详情',
            content: modalContent,
            buttons: [
                {
                    text: '关闭',
                    className: 'btn-primary'
                }
            ]
        });
    }

    exportUsers() {
        if (this.users.length === 0) {
            toast.warning('没有数据可导出');
            return;
        }

        const headers = ['Token', '注册时间', 'VIP状态', 'VIP到期时间', '账户状态'];
        const csvContent = [
            headers.join(','),
            ...this.users.map(user => [
                user.token,
                user.time,
                user.vipcode === '1' ? 'VIP' : '普通',
                user.viptime || '',
                user.tokencode === '500' ? '封禁' : '正常'
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `users_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        toast.success('用户数据已导出');
    }

    async refreshUsers() {
        const refreshBtn = utils.$('#refresh-users-btn');
        if (refreshBtn) {
            refreshBtn.classList.add('spinning');
        }

        try {
            await this.loadUsers();
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('spinning');
            }
        }
    }

    refresh() {
        this.loadUsers();
    }
}

// Export for global access
window.UsersPage = UsersPage;
