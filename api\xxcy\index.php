<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

include '../verify_vip.php';

// 获取请求中的token和手机号
$token = $_GET['token'] ?? $_POST['token'] ?? '';
$phone = $_GET['phone'] ?? $_POST['phone'] ?? '';

// 检查必填项
if (empty($token)) {
    echo json_encode([
        "code" => 400,
        "message" => "Token参数为必填项",
        '频道' => "@nfgzs",
        '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

if (empty($phone)) {
    echo json_encode([
        "code" => 400,
        "message" => "手机号参数为必填项",
        '频道' => "@nfgzs",
        '官网' => '【及时更新,更多接口】https://api.qnm6.top/'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        "code" => 400,
        "message" => "手机号格式不正确"
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 自定义限制（可以自由组合）
$vipTimeLimit = true;  // 是否启用会员时间限制
$vipCodeLimit = true;   // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// Token验证通过，处理xxcy业务逻辑
try {
    // 这里可以添加具体的业务逻辑
    // 例如：处理手机号相关的操作
    
    // 模拟处理时间
    usleep(100000); // 0.1秒
    
    // 返回成功响应
    echo json_encode([
        "code" => 200,
        "message" => "任务已提交，默认5分钟",
        "data" => [
            "phone" => $phone,
            "status" => "submitted",
            "estimated_time" => "5分钟",
            "submit_time" => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

} catch (Exception $e) {
    // 处理异常
    echo json_encode([
        "code" => 500,
        "message" => "服务器内部错误：" . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?>
