<?php
// verify_vip.php
// 日志记录函数
function logApiRequest($token) {
    $logDir = __DIR__ . '/api_logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    $date = date('Y-m-d');
    $logFile = $logDir . "/api_log_{$date}.txt";
    $time = date('Y-m-d H:i:s');
    $params = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
    $ip = getClientIP();

    // 从 URL 路径中提取 API 名称
    $requestUri = $_SERVER['REQUEST_URI'];
    $pathParts = explode('/', trim($requestUri, '/'));
    $apiName = isset($pathParts[1]) ? $pathParts[1] : 'unknown';

    $log = "[{$time}] IP: {$ip}, Token: {$token}, API: {$apiName}, Params: {$params}\n";
    file_put_contents($logFile, $log, FILE_APPEND);
}

function getClientIP() {
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

function verifyVipStatus($token, $callback = null) {
    // 记录日志
    logApiRequest($token);
    include 'db.php';  // 引入数据库连接配置

    // 查询用户信息
    $stmt = $mysqli->prepare("SELECT vipcode, viptime, tokencode FROM users WHERE token = ?");
    $stmt->bind_param('s', $token);
    $stmt->execute();
    $stmt->store_result();  // 使用 store_result() 来检查查询结果是否存在

    // 判断用户是否存在
    if ($stmt->num_rows === 0) {
        $stmt->close();
        return [
            'code' => 410,
            'message' => '未注册，请先注册。',
            ];
    }

    // 获取查询结果
    $stmt->bind_result($vipcode, $viptime, $tokencode);
    $stmt->fetch();
    $stmt->close();

    // 判断Token是否正常
    if ($tokencode !== '200') {
        return [
            'code' => 200,
            'message' => '80R 一月 解锁所有api 官方频道@idatas8',
            'shuju' => '80R 一月 解锁所有api 官方频道@idatas8',
            'imgurl' => '80R 一月 解锁所有api 官方频道@idatas8'
        ];
    }

    // 如果有回调函数，执行回调并返回错误
    if ($callback && is_callable($callback)) {
        $callbackResult = $callback($vipcode, $viptime);
        if ($callbackResult !== true) {
            return $callbackResult;
        }
    }

    // 如果所有条件通过，返回 true
    return true;
}

// 会员功能限制
function vipCodeLimit($vipcode) {
    if ($vipcode == '0') {
        return ['code' => 403, 'message' => '该功能为会员功能，非会员不可使用。'];
    }
    return true;
}

function vipTimeLimit($viptime) {
    $currentDate = new DateTime();
    // 将字符串格式的 viptime 转换为 DateTime 对象
    $vipExpireDate = DateTime::createFromFormat('Y-m-d H:i:s', $viptime);


    // 判断VIP到期时间是否大于35天
    $interval = $currentDate->diff($vipExpireDate);
    if ($interval->days <= 35) {
        return [
            'code' => 403, 
            'message' => "高级会员功能需要到期时间大于35天，当前到期时间：" . $vipExpireDate->format('Y-m-d H:i:s')
        ];
    }

    // 如果所有条件通过，返回 true
    return true;
}
?>
