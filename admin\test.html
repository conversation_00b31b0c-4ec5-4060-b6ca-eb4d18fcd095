<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面 - 管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .test-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .result-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-area pre {
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 12px;
            line-height: 1.4;
        }

        .loading {
            color: #667eea;
            font-style: italic;
        }

        .error {
            color: #e53e3e;
        }

        .success {
            color: #38a169;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-flask"></i> API测试页面</h1>
            <p>测试管理后台的各个API接口功能</p>
        </div>

        <div class="grid">
            <!-- 今日统计测试 -->
            <div class="test-section">
                <h3><i class="fas fa-chart-line"></i> 今日统计API</h3>
                <button class="test-btn" onclick="testTodayStats()">测试今日统计</button>
                <div class="result-area" id="today-stats-result">点击按钮测试API...</div>
            </div>

            <!-- 用户统计测试 -->
            <div class="test-section">
                <h3><i class="fas fa-users"></i> 用户统计API</h3>
                <button class="test-btn" onclick="testUserStats('stats')">用户统计</button>
                <button class="test-btn" onclick="testUserStats('vip')">VIP用户</button>
                <button class="test-btn" onclick="testUserStats('banned')">封禁用户</button>
                <div class="result-area" id="user-stats-result">点击按钮测试API...</div>
            </div>

            <!-- 卡密统计测试 -->
            <div class="test-section">
                <h3><i class="fas fa-credit-card"></i> 卡密统计API</h3>
                <button class="test-btn" onclick="testCardStats('stats')">卡密统计</button>
                <button class="test-btn" onclick="testCardStats('used')">已使用</button>
                <button class="test-btn" onclick="testCardStats('unused')">未使用</button>
                <div class="result-area" id="card-stats-result">点击按钮测试API...</div>
            </div>

            <!-- 日志统计测试 -->
            <div class="test-section">
                <h3><i class="fas fa-file-alt"></i> 日志统计API</h3>
                <button class="test-btn" onclick="testLogStats('api')">API日志</button>
                <button class="test-btn" onclick="testLogStats('demo')">Demo日志</button>
                <button class="test-btn" onclick="testLogStats('stats')">日志统计</button>
                <div class="result-area" id="log-stats-result">点击按钮测试API...</div>
            </div>

            <!-- 仪表盘统计测试 -->
            <div class="test-section">
                <h3><i class="fas fa-tachometer-alt"></i> 仪表盘统计</h3>
                <button class="test-btn" onclick="testDashboardStats()">仪表盘数据</button>
                <div class="result-area" id="dashboard-stats-result">点击按钮测试API...</div>
            </div>

            <!-- 系统信息测试 -->
            <div class="test-section">
                <h3><i class="fas fa-info-circle"></i> 系统信息</h3>
                <button class="test-btn" onclick="testSystemInfo()">获取系统信息</button>
                <div class="result-area" id="system-info-result">点击按钮测试API...</div>
            </div>
        </div>
    </div>

    <script>
        // 简化的API类
        class TestAPI {
            async request(endpoint, options = {}) {
                const url = `../admapi/${endpoint}`;
                const params = options.params || {};
                
                const queryString = Object.keys(params)
                    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
                    .join('&');
                
                const fullUrl = queryString ? `${url}?${queryString}` : url;
                
                const response = await fetch(fullUrl);
                return await response.json();
            }

            async getTodayStats() {
                return this.request('todaystats.php');
            }

            async getUserStats(type = 'all') {
                return this.request('userstats.php', { params: { type } });
            }

            async getCardStats(status = 'all') {
                return this.request('cardstats.php', { params: { status } });
            }

            async getLogStats(date = null, type = 'api') {
                const params = { type };
                if (date) params.date = date;
                return this.request('logstats.php', { params });
            }

            async getSystemInfo() {
                return this.request('msghq.php');
            }
        }

        const api = new TestAPI();

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre class="${isError ? 'error' : 'success'}">${JSON.stringify(data, null, 2)}</pre>`;
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '<div class="loading">加载中...</div>';
        }

        async function testTodayStats() {
            showLoading('today-stats-result');
            try {
                const result = await api.getTodayStats();
                showResult('today-stats-result', result);
            } catch (error) {
                showResult('today-stats-result', { error: error.message }, true);
            }
        }

        async function testUserStats(type) {
            showLoading('user-stats-result');
            try {
                const result = await api.getUserStats(type);
                showResult('user-stats-result', result);
            } catch (error) {
                showResult('user-stats-result', { error: error.message }, true);
            }
        }

        async function testCardStats(status) {
            showLoading('card-stats-result');
            try {
                const result = await api.getCardStats(status);
                showResult('card-stats-result', result);
            } catch (error) {
                showResult('card-stats-result', { error: error.message }, true);
            }
        }

        async function testLogStats(type) {
            showLoading('log-stats-result');
            try {
                const today = new Date().toISOString().split('T')[0];
                const result = await api.getLogStats(today, type);
                showResult('log-stats-result', result);
            } catch (error) {
                showResult('log-stats-result', { error: error.message }, true);
            }
        }

        async function testDashboardStats() {
            showLoading('dashboard-stats-result');
            try {
                // 模拟仪表盘统计（组合多个API）
                const [todayStats, userStats, cardStats] = await Promise.all([
                    api.getTodayStats(),
                    api.getUserStats('stats'),
                    api.getCardStats('stats')
                ]);

                const dashboardData = {
                    today: todayStats,
                    users: userStats,
                    cards: cardStats
                };

                showResult('dashboard-stats-result', dashboardData);
            } catch (error) {
                showResult('dashboard-stats-result', { error: error.message }, true);
            }
        }

        async function testSystemInfo() {
            showLoading('system-info-result');
            try {
                const result = await api.getSystemInfo();
                showResult('system-info-result', result);
            } catch (error) {
                showResult('system-info-result', { error: error.message }, true);
            }
        }

        // 页面加载完成后自动测试今日统计
        window.addEventListener('load', () => {
            console.log('API测试页面已加载');
        });
    </script>
</body>
</html>
