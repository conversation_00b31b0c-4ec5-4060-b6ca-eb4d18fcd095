<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
include '../../verify_vip.php';

// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = true;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 获取动态参数phone
$phone = isset($_GET['phone']) ? $_GET['phone'] : '';

// 判断手机号是否为1开头的11位
if (preg_match('/^1\d{10}$/', $phone)) {
    // 生成随机概率逻辑
    $randomValue = mt_rand(1, 100);  // 生成1到100之间的随机数

    // 确定号码类型
    if ($randomValue <= 50) {
        $status = '空号';
    } elseif ($randomValue <= 60) {
        $status = '实号';
    } elseif ($randomValue <= 80) {
        $status = '停机';
    } elseif ($randomValue <= 90) {
        $status = '库无';
    } elseif ($randomValue <= 95) {
        $status = '沉默号';
    } else {
        $status = '风险号';
    }

  // 设置时间范围：2014年1月1日到2024年1月1日
$startDate = strtotime('2014-01-01');  // 2014年1月1日
$endDate = strtotime('2024-01-01');    // 2024年1月1日

// 生成随机时间戳
$randomTimestamp = mt_rand($startDate, $endDate);

// 将时间戳转为日期格式
$activationTime = date('Y-m-d H:i', $randomTimestamp);

    // 输出检测结果
    $shuju = "手机号: $phone\n检测结果: $status\n开通时间: $activationTime\n";

    echo json_encode([
        'code' => 200,
        'message' => $shuju
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} else {
    // 如果手机号不符合条件，输出错误信息
    echo json_encode([
        'code' => 400,
        'message' => '请输入有效的手机号（1开头的11位数字）'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?>
