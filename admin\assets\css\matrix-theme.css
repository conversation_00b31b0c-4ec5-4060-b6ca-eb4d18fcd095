/* Matrix Theme CSS */

/* Matrix Theme Variables */
:root {
    /* Matrix Colors */
    --matrix-green: #00ff41;
    --matrix-dark-green: #008f11;
    --matrix-bg-dark: #0d1117;
    --matrix-bg-darker: #010409;
    --matrix-text-green: #00ff41;
    --matrix-text-dim: #39ff14;
    --matrix-border: #1f6feb;
    --matrix-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    --matrix-glow: 0 0 10px rgba(0, 255, 65, 0.5);
}

/* Matrix Theme Application */
[data-theme="matrix"] {
    --primary-color: var(--matrix-green);
    --primary-dark: var(--matrix-dark-green);
    --secondary-color: var(--matrix-dark-green);
    --accent-color: var(--matrix-text-dim);
    
    --bg-primary: var(--matrix-bg-dark);
    --bg-secondary: var(--matrix-bg-darker);
    --bg-tertiary: #161b22;
    
    --text-primary: var(--matrix-text-green);
    --text-secondary: var(--matrix-text-dim);
    --text-muted: #7d8590;
    
    --border-color: var(--matrix-border);
    --shadow-light: var(--matrix-shadow);
    --shadow-medium: var(--matrix-shadow);
    --shadow-heavy: var(--matrix-shadow);
    
    --success-color: var(--matrix-green);
    --warning-color: #f85149;
    --error-color: #da3633;
    --info-color: var(--matrix-text-dim);
}

/* Matrix Body Styling */
[data-theme="matrix"] body {
    background: var(--matrix-bg-darker);
    color: var(--matrix-text-green);
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
}

/* Matrix Sidebar */
[data-theme="matrix"] .sidebar {
    background: var(--matrix-bg-dark);
    border-right: 1px solid var(--matrix-border);
    box-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .logo {
    color: var(--matrix-green);
    text-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .nav-link {
    color: var(--matrix-text-dim);
    transition: all 0.3s ease;
}

[data-theme="matrix"] .nav-link:hover {
    color: var(--matrix-green);
    background: rgba(0, 255, 65, 0.1);
    box-shadow: inset 4px 0 0 var(--matrix-green);
    text-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .nav-item.active .nav-link {
    background: linear-gradient(135deg, rgba(0, 255, 65, 0.2), rgba(0, 143, 17, 0.2));
    color: var(--matrix-green);
    box-shadow: inset 4px 0 0 var(--matrix-green), var(--matrix-glow);
    text-shadow: var(--matrix-glow);
}

/* Matrix Header */
[data-theme="matrix"] .header {
    background: var(--matrix-bg-dark);
    border-bottom: 1px solid var(--matrix-border);
    box-shadow: 0 2px 10px rgba(0, 255, 65, 0.2);
}

[data-theme="matrix"] .header-left,
[data-theme="matrix"] .header-right {
    gap: 16px;
}

[data-theme="matrix"] .header-actions {
    gap: 12px;
}

[data-theme="matrix"] .page-title {
    color: var(--matrix-green);
    text-shadow: var(--matrix-glow);
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
}

[data-theme="matrix"] .theme-toggle,
[data-theme="matrix"] .refresh-btn,
[data-theme="matrix"] .mobile-menu-toggle {
    color: var(--matrix-text-dim);
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

[data-theme="matrix"] .theme-toggle:hover,
[data-theme="matrix"] .refresh-btn:hover,
[data-theme="matrix"] .mobile-menu-toggle:hover {
    color: var(--matrix-green);
    background: rgba(0, 255, 65, 0.1);
    border-color: var(--matrix-green);
    box-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .user-menu-toggle {
    color: var(--matrix-text-dim);
    border: 1px solid var(--matrix-border);
    background: rgba(0, 255, 65, 0.05);
}

[data-theme="matrix"] .user-menu-toggle:hover {
    color: var(--matrix-green);
    border-color: var(--matrix-green);
    box-shadow: var(--matrix-glow);
}

/* Matrix Cards */
[data-theme="matrix"] .stat-card,
[data-theme="matrix"] .chart-card,
[data-theme="matrix"] .quick-actions,
[data-theme="matrix"] .table-container {
    background: var(--matrix-bg-dark);
    border: 1px solid var(--matrix-border);
    box-shadow: var(--matrix-shadow);
}

[data-theme="matrix"] .stat-card::before {
    background: linear-gradient(135deg, var(--matrix-green), var(--matrix-dark-green));
    box-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .stat-card:hover {
    box-shadow: var(--matrix-glow), 0 8px 25px rgba(0, 255, 65, 0.2);
    border-color: var(--matrix-green);
}

[data-theme="matrix"] .stat-icon {
    background: linear-gradient(135deg, var(--matrix-green), var(--matrix-dark-green));
    box-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .stat-content h3 {
    color: var(--matrix-green);
    text-shadow: var(--matrix-glow);
}

/* Matrix Buttons */
[data-theme="matrix"] .action-btn,
[data-theme="matrix"] .btn-primary {
    background: linear-gradient(135deg, var(--matrix-green), var(--matrix-dark-green));
    border: 1px solid var(--matrix-green);
    box-shadow: var(--matrix-glow);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

[data-theme="matrix"] .action-btn:hover,
[data-theme="matrix"] .btn-primary:hover {
    box-shadow: var(--matrix-glow), 0 5px 15px rgba(0, 255, 65, 0.4);
    transform: translateY(-2px);
}

[data-theme="matrix"] .btn-secondary {
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid var(--matrix-border);
    color: var(--matrix-text-dim);
}

[data-theme="matrix"] .btn-secondary:hover {
    background: rgba(0, 255, 65, 0.2);
    border-color: var(--matrix-green);
    color: var(--matrix-green);
    box-shadow: var(--matrix-glow);
}

/* Matrix Tables */
[data-theme="matrix"] .data-table th {
    background: var(--matrix-bg-darker);
    color: var(--matrix-green);
    border-bottom: 1px solid var(--matrix-border);
    text-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .data-table td {
    color: var(--matrix-text-dim);
    border-bottom: 1px solid var(--matrix-border);
}

[data-theme="matrix"] .data-table tbody tr:hover {
    background: rgba(0, 255, 65, 0.05);
}

/* Matrix Forms */
[data-theme="matrix"] .form-control {
    background: var(--matrix-bg-dark);
    border: 1px solid var(--matrix-border);
    color: var(--matrix-text-green);
}

[data-theme="matrix"] .form-control:focus {
    border-color: var(--matrix-green);
    box-shadow: var(--matrix-glow);
}

[data-theme="matrix"] .form-control::placeholder {
    color: var(--text-muted);
}

/* Matrix Status Badges */
[data-theme="matrix"] .status-active {
    background: rgba(0, 255, 65, 0.2);
    color: var(--matrix-green);
}

[data-theme="matrix"] .status-vip {
    background: linear-gradient(135deg, var(--matrix-green), var(--matrix-text-dim));
    color: var(--matrix-bg-dark);
}

/* Matrix Modals */
[data-theme="matrix"] .modal {
    background: var(--matrix-bg-dark);
    border: 1px solid var(--matrix-border);
    box-shadow: var(--matrix-shadow);
}

[data-theme="matrix"] .modal-header {
    border-bottom: 1px solid var(--matrix-border);
    background: var(--matrix-bg-darker);
}

[data-theme="matrix"] .modal-title {
    color: var(--matrix-green);
    text-shadow: var(--matrix-glow);
}

/* Matrix Toast */
[data-theme="matrix"] .toast {
    background: var(--matrix-bg-dark);
    border: 1px solid var(--matrix-border);
    box-shadow: var(--matrix-shadow);
}

[data-theme="matrix"] .toast-success {
    border-left: 4px solid var(--matrix-green);
}

[data-theme="matrix"] .toast-success .toast-icon {
    color: var(--matrix-green);
}

/* Matrix Loading */
[data-theme="matrix"] .loading-screen {
    background: linear-gradient(135deg, var(--matrix-bg-darker), var(--matrix-bg-dark));
}

[data-theme="matrix"] .spinner {
    border-color: rgba(0, 255, 65, 0.3);
    border-top-color: var(--matrix-green);
    box-shadow: var(--matrix-glow);
}

/* Matrix Scrollbar */
[data-theme="matrix"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="matrix"] ::-webkit-scrollbar-track {
    background: var(--matrix-bg-darker);
}

[data-theme="matrix"] ::-webkit-scrollbar-thumb {
    background: var(--matrix-border);
    border-radius: 4px;
}

[data-theme="matrix"] ::-webkit-scrollbar-thumb:hover {
    background: var(--matrix-green);
    box-shadow: var(--matrix-glow);
}

/* Matrix Animation Effects */
[data-theme="matrix"] .matrix-text {
    animation: matrixGlow 2s ease-in-out infinite alternate;
}

@keyframes matrixGlow {
    from {
        text-shadow: 0 0 5px var(--matrix-green);
    }
    to {
        text-shadow: 0 0 20px var(--matrix-green), 0 0 30px var(--matrix-green);
    }
}

/* Matrix Spin Animation */
[data-theme="matrix"] .refresh-btn.spinning {
    animation: matrixSpin 1s linear infinite;
}

@keyframes matrixSpin {
    from {
        transform: rotate(0deg);
        color: var(--matrix-text-dim);
    }
    to {
        transform: rotate(360deg);
        color: var(--matrix-green);
    }
}

[data-theme="matrix"] .matrix-border {
    position: relative;
    overflow: hidden;
}

[data-theme="matrix"] .matrix-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--matrix-green), transparent);
    animation: matrixScan 2s linear infinite;
}

@keyframes matrixScan {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
