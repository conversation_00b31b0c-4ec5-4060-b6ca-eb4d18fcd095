<?php include 'includes/header.php'; ?>

<div class="card">
    <div class="header">
        <h1>🎥 K.S <small class="text-muted">Information Query</small></h1>
        <p class="text-muted">输入Token与K.S号，查询相关信息 📋</p>
    </div>

    <form class="ks-form" action="https://api.qnm6.top/api/dujia/index.php" method="GET">
        <div class="form-group">
            <label for="token" class="form-label">Token</label>
            <input type="text" class="form-control" id="token" name="token" required placeholder="请输入您的Token">
        </div>
        <div class="form-group">
            <label for="name" class="form-label">姓名</label>
            <input type="text" class="form-control" id="name" name="name" required placeholder="请输入姓名">
        </div>
        <div class="form-group">
            <label for="kuaishou" class="form-label">快手号</label>
            <input type="text" class="form-control" id="kuaishou" name="kuaishou" required placeholder="请输入快手号">
        </div>
        <input type="hidden" name="action" value="platform_id">
        <div class="form-group">
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-search"></i> 开始二要素核验
            </button>
        </div>
        <div class="note" style="margin-bottom:10px;">
            <p>说明：本接口为快手号+姓名二要素核验，60%概率实名返回，40%概率提示不匹配。每组姓名+快手号组合结果固定。</p>
        </div>
        <div class="loading">查询中，请稍候...</div>
        <pre class="result-area" style="display: none;"></pre>
    </form>
</div>

<script>
document.querySelector('.ks-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const loading = form.querySelector('.loading');
    const resultArea = form.querySelector('.result-area');
    
    loading.style.display = 'block';
    resultArea.style.display = 'none';
    
    const formData = new FormData(form);
    const queryString = new URLSearchParams(formData).toString();
    
    fetch('https://api.qnm6.top/api/dujia/index.php?' + queryString)
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';
            resultArea.style.display = 'block';
            
            // 格式化显示所有数据
            let formattedOutput = '';
            for (const key in data) {
                if (typeof data[key] === 'object') {
                    formattedOutput += `${key}:\n${JSON.stringify(data[key], null, 2)}\n\n`;
                } else {
                    formattedOutput += `${key}: ${data[key]}\n`;
                }
            }
            resultArea.textContent = formattedOutput;
        })
        .catch(error => {
            loading.style.display = 'none';
            resultArea.style.display = 'block';
            resultArea.textContent = '查询出错：' + error.message;
        });
});
</script>

<?php include 'includes/footer.php'; ?> 