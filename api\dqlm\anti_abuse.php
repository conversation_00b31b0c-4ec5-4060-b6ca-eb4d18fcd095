<?php
// 智能反滥用堵塞机制

function getClientIP() {
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

function checkAndBlockAbuse() {
    $ip = getClientIP();
    $cacheDir = __DIR__ . '/abuse_cache';
    
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0777, true);
    }
    
    $ipFile = $cacheDir . '/' . md5($ip) . '.txt';
    $currentTime = time();
    
    // 读取该IP的请求记录
    $requests = [];
    if (file_exists($ipFile)) {
        $content = file_get_contents($ipFile);
        $requests = $content ? explode("\n", trim($content)) : [];
    }
    
    // 清理1分钟前的记录
    $requests = array_filter($requests, function($timestamp) use ($currentTime) {
        return ($currentTime - intval($timestamp)) < 60;
    });
    
    // 添加当前请求时间
    $requests[] = $currentTime;
    
    // 保存更新后的记录
    file_put_contents($ipFile, implode("\n", $requests));
    
    // 检查请求频率
    $requestCount = count($requests);
    
    if ($requestCount > 10) {
        // 超过10次/分钟，进行极限堵塞
        header('Content-Type: text/html; charset=utf-8');
        echo "检测到异常请求，正在处理...";
        flush();
        
        set_time_limit(0); // 取消时间限制
        while(true) {
            sleep(30);
            echo str_repeat(' ', 2048); // 发送空白数据
            flush();
        }
        exit;
    } elseif ($requestCount > 5) {
        // 超过5次/分钟，中等堵塞
        sleep(rand(30, 120));
    } elseif ($requestCount > 3) {
        // 超过3次/分钟，轻度堵塞
        sleep(rand(10, 30));
    }
    
    // 正常请求，不堵塞
    return true;
}

// 执行检查
checkAndBlockAbuse();
?>
