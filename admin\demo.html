<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台演示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .demo-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-subtitle {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 40px;
        }

        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .demo-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .feature-description {
            opacity: 0.9;
            line-height: 1.6;
        }

        .tech-stack {
            text-align: center;
            margin-bottom: 60px;
        }

        .tech-stack h3 {
            font-size: 24px;
            margin-bottom: 30px;
        }

        .tech-items {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .screenshots {
            margin-bottom: 60px;
        }

        .screenshots h3 {
            text-align: center;
            font-size: 24px;
            margin-bottom: 30px;
        }

        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .screenshot-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .screenshot-placeholder {
            width: 100%;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 48px;
            opacity: 0.7;
        }

        .footer {
            text-align: center;
            padding: 40px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 768px) {
            .demo-title {
                font-size: 36px;
            }
            
            .demo-subtitle {
                font-size: 18px;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .tech-items {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">系统管理后台</h1>
            <p class="demo-subtitle">现代化企业级管理系统，功能完整，界面精美，响应式设计</p>
            <div class="demo-buttons">
                <a href="login.html" class="demo-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    立即体验
                </a>
                <a href="#features" class="demo-btn">
                    <i class="fas fa-info-circle"></i>
                    了解更多
                </a>
            </div>
        </div>

        <div id="features" class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <h3 class="feature-title">仪表盘概览</h3>
                <p class="feature-description">实时显示系统关键指标，包括用户统计、启动量、VIP用户等数据，提供快速操作入口和最近活动记录。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="feature-title">用户管理</h3>
                <p class="feature-description">完整的用户管理功能，支持查看用户列表、封禁/解封用户、VIP充值、用户详情查看等操作。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="feature-title">卡密管理</h3>
                <p class="feature-description">批量生成卡密、查看使用状态、管理卡密生命周期，支持导出和复制功能，操作简单高效。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h3 class="feature-title">系统设置</h3>
                <p class="feature-description">灵活的系统配置管理，包括软件版本、公告信息、下载链接、联系方式等各项参数设置。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="feature-title">数据统计</h3>
                <p class="feature-description">丰富的数据可视化图表，展示用户增长趋势、启动量统计、用户分布等关键业务指标。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="feature-title">操作日志</h3>
                <p class="feature-description">详细的操作日志记录，支持API日志和Demo日志查看，提供强大的搜索和筛选功能。</p>
            </div>
        </div>

        <div class="tech-stack">
            <h3>技术栈</h3>
            <div class="tech-items">
                <div class="tech-item">HTML5</div>
                <div class="tech-item">CSS3</div>
                <div class="tech-item">JavaScript ES6+</div>
                <div class="tech-item">Chart.js</div>
                <div class="tech-item">Font Awesome</div>
                <div class="tech-item">PHP 8.0+</div>
                <div class="tech-item">MySQL</div>
                <div class="tech-item">Nginx</div>
            </div>
        </div>

        <div class="screenshots">
            <h3>界面预览</h3>
            <div class="screenshot-grid">
                <div class="screenshot-item">
                    <div class="screenshot-placeholder">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h4>仪表盘</h4>
                </div>
                <div class="screenshot-item">
                    <div class="screenshot-placeholder">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>用户管理</h4>
                </div>
                <div class="screenshot-item">
                    <div class="screenshot-placeholder">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h4>卡密管理</h4>
                </div>
                <div class="screenshot-item">
                    <div class="screenshot-placeholder">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h4>数据统计</h4>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2025 系统管理后台. 现代化企业级管理解决方案.</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                <a href="login.html" style="color: white; text-decoration: none;">
                    <i class="fas fa-arrow-right"></i> 立即开始使用
                </a>
            </p>
        </div>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add some interactive effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
