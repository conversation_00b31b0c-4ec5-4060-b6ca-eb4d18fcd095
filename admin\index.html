<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理后台</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <!-- Matrix Theme -->
    <link rel="stylesheet" href="assets/css/matrix-theme.css">
    <link rel="stylesheet" href="assets/css/matrix-components.css">
    <link rel="stylesheet" href="assets/css/matrix-responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div id="app" class="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>系统加载中...</p>
            </div>
        </div>

        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="logo matrix-text">
                    <i class="fas fa-terminal"></i>
                    <span class="logo-text">MATRIX ADMIN</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item active" data-page="dashboard">
                        <a href="#" class="nav-link">
                            <i class="fas fa-desktop"></i>
                            <span>控制台</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="users">
                        <a href="#" class="nav-link">
                            <i class="fas fa-user-secret"></i>
                            <span>用户矩阵</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="cards">
                        <a href="#" class="nav-link">
                            <i class="fas fa-key"></i>
                            <span>密钥管理</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="settings">
                        <a href="#" class="nav-link">
                            <i class="fas fa-cogs"></i>
                            <span>系统配置</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="statistics">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>数据分析</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="logs">
                        <a href="#" class="nav-link">
                            <i class="fas fa-code"></i>
                            <span>系统日志</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title" class="page-title matrix-text">MATRIX CONSOLE</h1>
                </div>

                <div class="header-right">
                    <div class="header-actions">
                        <button id="theme-toggle" class="theme-toggle" title="切换主题">
                            <i class="fas fa-moon"></i>
                        </button>
                        <button id="refresh-btn" class="refresh-btn" title="刷新数据">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="user-menu">
                        <button class="user-menu-toggle">
                            <i class="fas fa-user-ninja"></i>
                            <span id="current-username">ADMIN</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-menu-dropdown">
                            <a href="#" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                个人资料
                            </a>
                            <a href="#" class="dropdown-item">
                                <i class="fas fa-cog"></i>
                                设置
                            </a>
                            <a href="#" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Dashboard Page -->
                <div id="dashboard-page" class="page active">
                    <div class="dashboard-grid">
                        <!-- Stats Cards -->
                        <div class="stats-row">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="total-users">-</h3>
                                    <p>总用户数</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="vip-users">-</h3>
                                    <p>VIP用户</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="today-launches">-</h3>
                                    <p>今日启动</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="today-registers">-</h3>
                                    <p>今日注册</p>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Row -->
                        <div class="charts-row">
                            <div class="chart-card">
                                <div class="card-header">
                                    <h3>用户增长趋势</h3>
                                </div>
                                <div class="card-body">
                                    <canvas id="userGrowthChart"></canvas>
                                </div>
                            </div>
                            <div class="chart-card">
                                <div class="card-header">
                                    <h3>启动量统计</h3>
                                </div>
                                <div class="card-body">
                                    <canvas id="launchStatsChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <h3>快速操作</h3>
                            <div class="action-buttons">
                                <button class="action-btn" data-action="generate-cards">
                                    <i class="fas fa-plus"></i>
                                    生成卡密
                                </button>
                                <button class="action-btn" data-action="user-management">
                                    <i class="fas fa-user-cog"></i>
                                    用户管理
                                </button>
                                <button class="action-btn" data-action="system-settings">
                                    <i class="fas fa-cogs"></i>
                                    系统设置
                                </button>
                                <button class="action-btn" data-action="view-logs">
                                    <i class="fas fa-eye"></i>
                                    查看日志
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other pages will be loaded here dynamically -->
                <div id="users-page" class="page"></div>
                <div id="cards-page" class="page"></div>
                <div id="settings-page" class="page"></div>
                <div id="statistics-page" class="page"></div>
                <div id="logs-page" class="page"></div>
            </div>
        </main>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/api.js"></script>
    <script src="assets/js/components.js"></script>
    <script src="assets/js/matrix-effects.js"></script>
    <script src="assets/js/pages/dashboard.js"></script>
    <script src="assets/js/pages/users.js"></script>
    <script src="assets/js/pages/cards.js"></script>
    <script src="assets/js/pages/settings.js"></script>
    <script src="assets/js/pages/statistics.js"></script>
    <script src="assets/js/pages/logs.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>
