<?php
/*
 * @LastEditors: CloudZA(云之安) <<EMAIL>>
 * @hitokoto: 一场秋雨一场凉，秋心酌满泪为霜。
 * Copyright (c) 2022 by CloudZA, All Rights Reserved.
 */

// 处理tmpview路由
$requestUri = $_SERVER['REQUEST_URI'];
if (preg_match('#^/tmpview/([a-zA-Z0-9]+)/?#', $requestUri, $matches)) {
    // 包含tmpview页面
    include 'tmpview/index.php';
    exit;
}

//if (!file_exists('./install/install.lock')) {
//    header("Location: ../install");
//    return true;
//}
require_once( 'include/common.php' );
require_once( 'include/Route.class.php' );
$count_api = Db::table('api_list')->count();

$_GET && SafeFilter($_GET);
$_POST && SafeFilter($_POST);
$_COOKIE && SafeFilter($_COOKIE);
function SafeFilter (&$arr): void
{
    $ra = array('/([\x00-\x08,\x0b-\x0c,\x0e-\x19])/' , '/script/' , '/javascript/' , '/vbscript/' , '/expression/' , '/applet/' , '/meta/' , '/xml/' , '/blink/' , '/link/' , '/style/' , '/embed/' , '/object/' , '/frame/' , '/layer/' , '/title/' , '/bgsound/' , '/base/' , '/onload/' , '/onunload/' , '/onchange/' , '/onsubmit/' , '/onreset/' , '/onselect/' , '/onblur/' , '/onfocus/' , '/onabort/' , '/onkeydown/' , '/onkeypress/' , '/onkeyup/' , '/onclick/' , '/ondblclick/' , '/onmousedown/' , '/onmousemove/' , '/onmouseout/' , '/onmouseover/' , '/onmouseup/' , '/onunload/');
    if (is_array($arr)) {
        foreach ($arr as $key => $value) {
            if ( !is_array($value)) {
                if ( !ini_get('magic_quotes_gpc')) {
                    $value = addslashes($value);
                }
                $value = preg_replace($ra , '' , $value);
                $arr[ $key ] = htmlentities(strip_tags($value));
            } else {
                SafeFilter($arr[ $key ]);
            }
        }
    }
}

$route = new Route();
?>
<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="refresh" content="5;url=https://www.qnm6.top">
    <title>域名已更换</title>
    <style>
        body { background: #f8f9fa; margin: 0; padding: 0; }
        .notice {
            max-width: 500px;
            margin: 100px auto 0 auto;
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
            border-radius: 8px;
            padding: 40px 30px;
            text-align: center;
            font-size: 1.3em;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        a { color: #007bff; text-decoration: underline; }
    </style>
</head>
<body>
    <div class="notice">
        已经更换域名，5秒后自动跳转到新域名：<br>
        <a href="https://www.qnm6.top">https://www.qnm6.top</a>
    </div>
</body>
</html>