// Cards Management Page

class CardsPage {
    constructor() {
        this.cards = [];
        this.dataTable = null;
        this.currentFilter = 'all'; // all, used, unused
        this.init();
    }

    init() {
        this.renderPage();
        this.loadCards();
    }

    renderPage() {
        const container = utils.$('#cards-page');
        if (!container) return;

        container.innerHTML = `
            <div class="cards-page">
                <div class="page-header">
                    <div class="page-header-content">
                        <h2>卡密管理</h2>
                        <p>管理系统中的所有卡密，包括生成、查看使用状态等操作</p>
                    </div>
                    <div class="page-header-actions">
                        <button class="btn btn-primary" id="generate-cards-btn">
                            <i class="fas fa-plus"></i>
                            生成卡密
                        </button>
                        <button class="btn btn-secondary" id="refresh-cards-btn">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <div class="cards-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-cards-count">0</h3>
                            <p>总卡密数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="used-cards-count">0</h3>
                            <p>已使用</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="unused-cards-count">0</h3>
                            <p>未使用</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="usage-rate">0%</h3>
                            <p>使用率</p>
                        </div>
                    </div>
                </div>

                <div class="cards-filters">
                    <div class="filter-tabs">
                        <button class="filter-tab active" data-filter="all">
                            <i class="fas fa-list"></i>
                            全部卡密
                        </button>
                        <button class="filter-tab" data-filter="unused">
                            <i class="fas fa-clock"></i>
                            未使用
                        </button>
                        <button class="filter-tab" data-filter="used">
                            <i class="fas fa-check-circle"></i>
                            已使用
                        </button>
                    </div>
                </div>

                <div id="cards-table-container"></div>
            </div>
        `;

        this.bindEvents();
        this.initDataTable();
    }

    bindEvents() {
        // Generate cards button
        const generateBtn = utils.$('#generate-cards-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.showGenerateCardsModal();
            });
        }

        // Refresh button
        const refreshBtn = utils.$('#refresh-cards-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCards();
            });
        }

        // Filter tabs
        utils.delegate(document, '.filter-tab', 'click', (e) => {
            const filter = e.target.dataset.filter;
            this.setFilter(filter);
        });
    }

    initDataTable() {
        const container = utils.$('#cards-table-container');
        if (!container) return;

        this.dataTable = new DataTable(container, {
            title: '卡密列表',
            columns: [
                {
                    key: 'kami',
                    title: '卡密',
                    render: (value) => `
                        <div class="card-code-container">
                            <code class="card-code">${value}</code>
                            <button class="btn btn-sm btn-secondary copy-card-btn" data-card="${value}">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    `
                },
                {
                    key: 'kamitime',
                    title: '天数',
                    render: (value) => `<span class="card-days">${value} 天</span>`
                },
                {
                    key: 'kamicode',
                    title: '状态',
                    render: (value, row) => {
                        if (value === '1') {
                            return '<span class="status-badge status-active">已使用</span>';
                        }
                        return '<span class="status-badge status-pending">未使用</span>';
                    }
                },
                {
                    key: 'oktoken',
                    title: '使用者',
                    render: (value) => {
                        if (!value) return '-';
                        return `<code class="user-token">${value}</code>`;
                    }
                },
                {
                    key: 'oktime',
                    title: '使用时间',
                    render: (value) => {
                        if (!value) return '-';
                        return utils.formatDate(value);
                    }
                },
                {
                    key: 'actions',
                    title: '操作',
                    render: (value, row) => {
                        const isUsed = row.kamicode === '1';
                        
                        return `
                            <div class="action-buttons">
                                ${!isUsed ? `
                                    <button class="btn btn-sm btn-primary use-card" data-kami="${row.kami}">
                                        <i class="fas fa-play"></i>
                                        使用
                                    </button>
                                ` : ''}
                                <button class="btn btn-sm btn-info card-details" data-kami="${row.kami}">
                                    <i class="fas fa-eye"></i>
                                    详情
                                </button>
                            </div>
                        `;
                    }
                }
            ],
            data: [],
            pagination: true,
            pageSize: 20,
            searchable: true,
            sortable: true,
            actions: [
                {
                    name: 'export',
                    text: '导出数据',
                    icon: 'fas fa-download',
                    className: 'btn-secondary',
                    onClick: () => this.exportCards()
                }
            ]
        });

        // Bind table action events
        utils.delegate(utils.$('#cards-table-container'), '.copy-card-btn', 'click', async (e) => {
            e.stopPropagation();
            const card = e.target.dataset.card;
            const success = await utils.copyToClipboard(card);
            if (success) {
                toast.success('卡密已复制到剪贴板');
            } else {
                toast.error('复制失败');
            }
        });

        utils.delegate(utils.$('#cards-table-container'), '.use-card', 'click', (e) => {
            const kami = e.target.dataset.kami;
            this.showUseCardModal(kami);
        });

        utils.delegate(utils.$('#cards-table-container'), '.card-details', 'click', (e) => {
            const kami = e.target.dataset.kami;
            this.showCardDetails(kami);
        });
    }

    async loadCards() {
        try {
            loading.show('加载卡密数据...');

            // Use new enhanced card stats API
            const [cardsResponse, statsResponse] = await Promise.all([
                api.getCardStats('all'),
                api.getCardStats('stats')
            ]);

            loading.hide();

            if (cardsResponse.code === 200) {
                this.cards = cardsResponse.kamis || [];
                this.cardStats = statsResponse.stats || {};
                this.updateStats();
                this.filterAndUpdateTable();
                toast.success('卡密数据加载成功');
            } else {
                toast.error(cardsResponse.message || '加载卡密数据失败');
            }
        } catch (error) {
            loading.hide();
            console.error('加载卡密数据失败:', error);
            toast.error('加载卡密数据失败: ' + error.message);
        }
    }

    updateStats() {
        // Use stats from API if available, otherwise calculate from cards array
        const stats = this.cardStats || {};

        const totalCards = stats.total || this.cards.length;
        const usedCards = stats.used || this.cards.filter(card => card.kamicode === '1').length;
        const unusedCards = stats.unused || this.cards.filter(card => card.kamicode === '0').length;
        const usageRate = stats.usage_rate || (totalCards > 0 ? Math.round((usedCards / totalCards) * 100) : 0);

        utils.$('#total-cards-count').textContent = utils.formatNumber(totalCards);
        utils.$('#used-cards-count').textContent = utils.formatNumber(usedCards);
        utils.$('#unused-cards-count').textContent = utils.formatNumber(unusedCards);
        utils.$('#usage-rate').textContent = usageRate + '%';
    }

    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active tab
        utils.$$('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        utils.$(`[data-filter="${filter}"]`).classList.add('active');
        
        this.filterAndUpdateTable();
    }

    filterAndUpdateTable() {
        let filteredCards = [...this.cards];
        
        switch (this.currentFilter) {
            case 'used':
                filteredCards = this.cards.filter(card => card.kamicode === '1');
                break;
            case 'unused':
                filteredCards = this.cards.filter(card => card.kamicode === '0');
                break;
            default:
                // Show all cards
                break;
        }
        
        this.dataTable.setData(filteredCards);
    }

    async showGenerateCardsModal() {
        const modalContent = `
            <div class="form-group">
                <label class="form-label">卡密天数</label>
                <input type="number" id="card-days" class="form-control" placeholder="请输入天数" min="1" value="30">
                <small class="form-text">设置卡密的有效天数</small>
            </div>
            <div class="form-group">
                <label class="form-label">生成数量</label>
                <input type="number" id="card-count" class="form-control" placeholder="请输入数量" min="1" max="100" value="10">
                <small class="form-text">一次最多生成100张卡密</small>
            </div>
        `;

        modal.show({
            title: '生成卡密',
            content: modalContent,
            buttons: [
                {
                    text: '取消',
                    className: 'btn-secondary'
                },
                {
                    text: '生成',
                    className: 'btn-primary',
                    onClick: async () => {
                        const days = utils.$('#card-days').value;
                        const count = utils.$('#card-count').value;

                        if (!days || !count) {
                            toast.error('请填写完整信息');
                            return false;
                        }

                        if (days < 1 || count < 1 || count > 100) {
                            toast.error('参数范围错误');
                            return false;
                        }

                        try {
                            loading.show('正在生成卡密...');
                            const result = await api.generateCards(days, count);
                            loading.hide();

                            if (result.code === 200) {
                                toast.success(`成功生成 ${result.kamis.length} 张卡密`);
                                this.loadCards(); // Refresh cards list
                                this.showGeneratedCards(result.kamis, days);
                            } else {
                                toast.error(result.message || '生成失败');
                            }
                        } catch (error) {
                            loading.hide();
                            toast.error('生成卡密失败: ' + error.message);
                        }
                    }
                }
            ]
        });
    }

    showGeneratedCards(cards, days) {
        const cardsList = cards.map(card => `
            <div class="generated-card">
                <span class="card-code">${card}</span>
                <button class="btn btn-sm btn-secondary copy-card" data-card="${card}">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        `).join('');

        const modalContent = `
            <p>成功生成 ${cards.length} 张 ${days} 天的卡密：</p>
            <div class="generated-cards-list">
                ${cardsList}
            </div>
            <div class="mt-3">
                <button class="btn btn-primary" id="copy-all-cards">
                    <i class="fas fa-copy"></i>
                    复制全部
                </button>
                <button class="btn btn-secondary" id="export-cards">
                    <i class="fas fa-download"></i>
                    导出文件
                </button>
            </div>
        `;

        modal.show({
            title: '生成的卡密',
            content: modalContent,
            size: 'large',
            buttons: [
                {
                    text: '关闭',
                    className: 'btn-primary'
                }
            ],
            onShow: (modalElement) => {
                // Copy single card
                utils.delegate(modalElement, '.copy-card', 'click', async (e) => {
                    const card = e.target.dataset.card;
                    const success = await utils.copyToClipboard(card);
                    if (success) {
                        toast.success('卡密已复制到剪贴板');
                    } else {
                        toast.error('复制失败');
                    }
                });

                // Copy all cards
                const copyAllBtn = modalElement.querySelector('#copy-all-cards');
                copyAllBtn.addEventListener('click', async () => {
                    const allCards = cards.join('\n');
                    const success = await utils.copyToClipboard(allCards);
                    if (success) {
                        toast.success('所有卡密已复制到剪贴板');
                    } else {
                        toast.error('复制失败');
                    }
                });

                // Export cards
                const exportBtn = modalElement.querySelector('#export-cards');
                exportBtn.addEventListener('click', () => {
                    const content = cards.join('\n');
                    const blob = new Blob([content], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `cards_${days}days_${new Date().toISOString().split('T')[0]}.txt`;
                    a.click();
                    URL.revokeObjectURL(url);
                    toast.success('卡密文件已导出');
                });
            }
        });
    }

    async showUseCardModal(kami) {
        const token = await modal.prompt('请输入要使用此卡密的用户Token', '', '使用卡密');
        
        if (!token) return;

        if (!utils.validate.required(token)) {
            toast.error('请输入有效的Token');
            return;
        }

        try {
            loading.show('正在使用卡密...');
            const response = await api.useCard(token, kami);
            loading.hide();

            if (response.code === 200) {
                toast.success('卡密使用成功');
                this.loadCards(); // Refresh cards list
            } else {
                toast.error(response.message || '使用卡密失败');
            }
        } catch (error) {
            loading.hide();
            console.error('使用卡密失败:', error);
            toast.error('使用卡密失败: ' + error.message);
        }
    }

    showCardDetails(kami) {
        const card = this.cards.find(c => c.kami === kami);
        if (!card) {
            toast.error('卡密不存在');
            return;
        }

        const status = card.kamicode === '1' ? '已使用' : '未使用';
        const user = card.oktoken || '无';
        const useTime = card.oktime ? utils.formatDate(card.oktime) : '无';

        const modalContent = `
            <div class="card-details">
                <div class="detail-item">
                    <label>卡密:</label>
                    <span class="detail-value">
                        <code>${card.kami}</code>
                        <button class="btn btn-sm btn-secondary ml-2 copy-detail-card" data-card="${card.kami}">
                            <i class="fas fa-copy"></i>
                        </button>
                    </span>
                </div>
                <div class="detail-item">
                    <label>天数:</label>
                    <span class="detail-value">${card.kamitime} 天</span>
                </div>
                <div class="detail-item">
                    <label>状态:</label>
                    <span class="detail-value">
                        <span class="status-badge ${card.kamicode === '1' ? 'status-active' : 'status-pending'}">
                            ${status}
                        </span>
                    </span>
                </div>
                <div class="detail-item">
                    <label>使用者:</label>
                    <span class="detail-value">
                        ${user !== '无' ? `<code>${user}</code>` : user}
                    </span>
                </div>
                <div class="detail-item">
                    <label>使用时间:</label>
                    <span class="detail-value">${useTime}</span>
                </div>
            </div>
        `;

        modal.show({
            title: '卡密详情',
            content: modalContent,
            buttons: [
                {
                    text: '关闭',
                    className: 'btn-primary'
                }
            ],
            onShow: (modalElement) => {
                const copyBtn = modalElement.querySelector('.copy-detail-card');
                if (copyBtn) {
                    copyBtn.addEventListener('click', async () => {
                        const card = copyBtn.dataset.card;
                        const success = await utils.copyToClipboard(card);
                        if (success) {
                            toast.success('卡密已复制到剪贴板');
                        } else {
                            toast.error('复制失败');
                        }
                    });
                }
            }
        });
    }

    exportCards() {
        if (this.cards.length === 0) {
            toast.warning('没有数据可导出');
            return;
        }

        const headers = ['卡密', '天数', '状态', '使用者', '使用时间'];
        const csvContent = [
            headers.join(','),
            ...this.cards.map(card => [
                card.kami,
                card.kamitime,
                card.kamicode === '1' ? '已使用' : '未使用',
                card.oktoken || '',
                card.oktime || ''
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cards_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        toast.success('卡密数据已导出');
    }

    async refreshCards() {
        const refreshBtn = utils.$('#refresh-cards-btn');
        if (refreshBtn) {
            refreshBtn.classList.add('spinning');
        }

        try {
            await this.loadCards();
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('spinning');
            }
        }
    }

    refresh() {
        this.loadCards();
    }
}

// Export for global access
window.CardsPage = CardsPage;
