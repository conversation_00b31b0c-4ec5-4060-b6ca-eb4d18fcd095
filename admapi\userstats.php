<?php
/**
 * 用户统计API
 */

require_once '../db.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $type = $_GET['type'] ?? 'stats';
    
    if (!isset($mysqli) || !$mysqli) {
        throw new Exception('数据库连接失败');
    }
    
    $response = [
        'code' => 200,
        'message' => 'success'
    ];
    
    if ($type === 'stats') {
        $stats = [];
        
        // 总用户数
        $result = $mysqli->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            $stats['total'] = intval($row['count']);
        } else {
            $stats['total'] = 0;
        }
        
        // VIP用户数
        $result = $mysqli->query("SELECT COUNT(*) as count FROM users WHERE vipcode = '1'");
        if ($result) {
            $row = $result->fetch_assoc();
            $stats['vip'] = intval($row['count']);
        } else {
            $stats['vip'] = 0;
        }
        
        // 封禁用户数
        $result = $mysqli->query("SELECT COUNT(*) as count FROM users WHERE tokencode = '500'");
        if ($result) {
            $row = $result->fetch_assoc();
            $stats['banned'] = intval($row['count']);
        } else {
            $stats['banned'] = 0;
        }
        
        // 普通用户数
        $stats['normal'] = $stats['total'] - $stats['vip'] - $stats['banned'];
        
        // 今日注册
        $today = date('Y-m-d');
        $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM users WHERE DATE(time) = ?");
        if ($stmt) {
            $stmt->bind_param("s", $today);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $stats['today_registers'] = intval($row['count']);
            $stmt->close();
        } else {
            $stats['today_registers'] = 0;
        }
        
        // 最近注册用户
        $result = $mysqli->query("SELECT token, time, vipcode, tokencode FROM users ORDER BY time DESC LIMIT 10");
        $recentUsers = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $recentUsers[] = $row;
            }
        }
        $stats['recent_users'] = $recentUsers;
        
        // 注册趋势（最近7天）
        $dailyStats = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM users WHERE DATE(time) = ?");
            if ($stmt) {
                $stmt->bind_param("s", $date);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                $dailyStats[] = [
                    'date' => $date,
                    'count' => intval($row['count'])
                ];
                $stmt->close();
            }
        }
        $stats['daily_registers'] = $dailyStats;
        
        $response['stats'] = $stats;
        
    } else {
        // 获取用户列表
        $sql = "SELECT token, time, vipcode, viptime, tokencode FROM users";
        
        switch ($type) {
            case 'vip':
                $sql .= " WHERE vipcode = '1' ORDER BY viptime DESC";
                break;
            case 'banned':
                $sql .= " WHERE tokencode = '500' ORDER BY time DESC";
                break;
            case 'normal':
                $sql .= " WHERE vipcode = '0' AND tokencode = '200' ORDER BY time DESC";
                break;
            default:
                $sql .= " ORDER BY time DESC";
                break;
        }
        
        $result = $mysqli->query($sql);
        $users = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $users[] = $row;
            }
        }
        $response['users'] = $users;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'code' => 500,
        'message' => '获取用户数据失败: ' . $e->getMessage()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>
