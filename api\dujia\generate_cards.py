import json
import random
import string
from datetime import datetime

def generate_card_code(length=8):
    """生成指定长度的随机卡密"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def create_cards(num_cards=1, times_per_card=10):
    """创建指定数量的卡密"""
    cards_file = 'cards.json'
    try:
        with open(cards_file, 'r', encoding='utf-8') as f:
            cards = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        cards = {}
    
    new_cards = {}
    for _ in range(num_cards):
        while True:
            card_code = generate_card_code()
            if card_code not in cards:
                break
        
        cards[card_code] = {
            'remaining_times': times_per_card,
            'total_times': times_per_card,
            'created_at': datetime.now().strftime('%Y-%m-%d')
        }
        new_cards[card_code] = cards[card_code]
    
    # 保存所有卡密信息
    with open(cards_file, 'w', encoding='utf-8') as f:
        json.dump(cards, f, indent=4, ensure_ascii=False)
    
    return new_cards

if __name__ == '__main__':
    # 生成5个新卡密，每个卡密10次使用机会
    new_cards = create_cards(5, 10)
    print('成功生成以下卡密：')
    for card_code, info in new_cards.items():
        print(f'卡密: {card_code}, 使用次数: {info["total_times"]}')