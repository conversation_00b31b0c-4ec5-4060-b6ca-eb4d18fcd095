<?php
// 快速API测试
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>快速API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>API测试结果</h1>
    
    <?php
    $apis = [
        'todaystats.php' => '今日统计API',
        'userstats.php?type=stats' => '用户统计API',
        'cardstats.php?status=stats' => '卡密统计API',
        'logstats.php?type=api' => '日志统计API'
    ];
    
    foreach ($apis as $endpoint => $name) {
        echo "<div class='test'>";
        echo "<h3>测试: {$name}</h3>";
        echo "<p>端点: {$endpoint}</p>";
        
        $url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/../admapi/{$endpoint}";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $result = @file_get_contents($url, false, $context);
        
        if ($result === false) {
            echo "<div class='error'>";
            echo "<p>❌ 请求失败</p>";
            $error = error_get_last();
            if ($error) {
                echo "<p>错误: " . htmlspecialchars($error['message']) . "</p>";
            }
            echo "</div>";
        } else {
            $json = json_decode($result, true);
            if ($json === null) {
                echo "<div class='error'>";
                echo "<p>❌ 返回的不是有效JSON</p>";
                echo "<pre>" . htmlspecialchars($result) . "</pre>";
                echo "</div>";
            } else {
                echo "<div class='success'>";
                echo "<p>✅ 请求成功</p>";
                echo "<pre>" . htmlspecialchars(json_encode($json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
                echo "</div>";
            }
        }
        
        echo "</div>";
    }
    ?>
    
    <hr>
    <p><a href="test.html">返回完整测试页面</a></p>
    <p><a href="simple_test.html">返回简单测试页面</a></p>
</body>
</html>
