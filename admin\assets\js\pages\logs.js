// Logs Page

class LogsPage {
    constructor() {
        this.logs = [];
        this.demoLogs = [];
        this.dataTable = null;
        this.currentLogType = 'api'; // api or demo
        this.currentDate = new Date().toISOString().split('T')[0];
        this.init();
    }

    init() {
        this.renderPage();
        this.loadLogs();
    }

    renderPage() {
        const container = utils.$('#logs-page');
        if (!container) return;

        container.innerHTML = `
            <div class="logs-page">
                <div class="page-header">
                    <div class="page-header-content">
                        <h2>操作日志</h2>
                        <p>查看系统的所有操作日志和用户活动记录</p>
                    </div>
                    <div class="page-header-actions">
                        <button class="btn btn-primary" id="export-logs-btn">
                            <i class="fas fa-download"></i>
                            导出日志
                        </button>
                        <button class="btn btn-secondary" id="refresh-logs-btn">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <!-- Log Controls -->
                <div class="logs-controls">
                    <div class="log-filters">
                        <div class="filter-group">
                            <label class="form-label">日志类型</label>
                            <div class="log-type-tabs">
                                <button class="log-type-tab active" data-type="api">
                                    <i class="fas fa-code"></i>
                                    API日志
                                </button>
                                <button class="log-type-tab" data-type="demo">
                                    <i class="fas fa-play"></i>
                                    Demo日志
                                </button>
                            </div>
                        </div>
                        
                        <div class="filter-group">
                            <label class="form-label">选择日期</label>
                            <input type="date" id="log-date-picker" class="form-control" value="${this.currentDate}">
                        </div>
                        
                        <div class="filter-group">
                            <label class="form-label">用户筛选</label>
                            <input type="text" id="user-filter" class="form-control" placeholder="输入用户Token">
                        </div>
                        
                        <div class="filter-group">
                            <label class="form-label">API筛选</label>
                            <select id="api-filter" class="form-control">
                                <option value="">全部API</option>
                                <option value="demo">demo</option>
                                <option value="zhuce">zhuce</option>
                                <option value="gh1">gh1</option>
                                <option value="eys">eys</option>
                                <option value="lm">lm</option>
                                <option value="dqlm">dqlm</option>
                                <option value="family">family</option>
                                <option value="qq">qq</option>
                                <option value="dw">dw</option>
                                <option value="jz">jz</option>
                                <option value="zyj">zyj</option>
                                <option value="dujia">dujia</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Log Statistics -->
                <div class="logs-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-logs-count">0</h3>
                            <p>总日志数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="unique-users-count">0</h3>
                            <p>活跃用户</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="latest-log-time">-</h3>
                            <p>最新日志</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="api-calls-count">0</h3>
                            <p>API调用</p>
                        </div>
                    </div>
                </div>

                <!-- Logs Table -->
                <div id="logs-table-container"></div>
            </div>
        `;

        this.bindEvents();
        this.initDataTable();
    }

    bindEvents() {
        // Export button
        const exportBtn = utils.$('#export-logs-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportLogs();
            });
        }

        // Refresh button
        const refreshBtn = utils.$('#refresh-logs-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshLogs();
            });
        }

        // Log type tabs
        utils.delegate(document, '.log-type-tab', 'click', (e) => {
            const type = e.target.dataset.type;
            this.setLogType(type);
        });

        // Date picker
        const datePicker = utils.$('#log-date-picker');
        if (datePicker) {
            datePicker.addEventListener('change', (e) => {
                this.currentDate = e.target.value;
                this.loadLogs();
            });
        }

        // Filters
        const userFilter = utils.$('#user-filter');
        if (userFilter) {
            userFilter.addEventListener('input', utils.debounce(() => {
                this.applyFilters();
            }, 300));
        }

        const apiFilter = utils.$('#api-filter');
        if (apiFilter) {
            apiFilter.addEventListener('change', () => {
                this.applyFilters();
            });
        }
    }

    initDataTable() {
        const container = utils.$('#logs-table-container');
        if (!container) return;

        this.dataTable = new DataTable(container, {
            title: 'API日志列表',
            columns: this.getTableColumns(),
            data: [],
            pagination: true,
            pageSize: 50,
            searchable: true,
            sortable: true,
            actions: [
                {
                    name: 'clear-filters',
                    text: '清除筛选',
                    icon: 'fas fa-times',
                    className: 'btn-secondary',
                    onClick: () => this.clearFilters()
                }
            ]
        });
    }

    getTableColumns() {
        if (this.currentLogType === 'api') {
            return [
                {
                    key: 'timestamp',
                    title: '时间',
                    render: (value) => `<span class="log-time">${value}</span>`
                },
                {
                    key: 'token',
                    title: '用户Token',
                    render: (value) => value ? `<code class="user-token">${value}</code>` : '-'
                },
                {
                    key: 'api',
                    title: 'API',
                    render: (value, row) => `
                        <div class="api-info">
                            <span class="api-name">${value}</span>
                            <small class="api-description">${row.api_name || value}</small>
                        </div>
                    `
                },
                {
                    key: 'ips',
                    title: 'IP地址',
                    render: (value) => {
                        if (Array.isArray(value) && value.length > 0) {
                            return `<span class="ip-address">${value[0]}</span>`;
                        }
                        return '-';
                    }
                },
                {
                    key: 'params',
                    title: '参数',
                    render: (value, row) => {
                        if (!value || typeof value !== 'object') return '-';
                        
                        const paramCount = Object.keys(value).length;
                        return `
                            <button class="btn btn-sm btn-info view-params" data-params='${JSON.stringify(value)}'>
                                <i class="fas fa-eye"></i>
                                查看 (${paramCount})
                            </button>
                        `;
                    }
                },
                {
                    key: 'actions',
                    title: '操作',
                    render: (value, row) => `
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-secondary copy-log" data-log='${JSON.stringify(row)}'>
                                <i class="fas fa-copy"></i>
                                复制
                            </button>
                        </div>
                    `
                }
            ];
        } else {
            return [
                {
                    key: 'timestamp',
                    title: '时间',
                    render: (value) => `<span class="log-time">${value}</span>`
                },
                {
                    key: 'cardKey',
                    title: '卡密',
                    render: (value) => value ? `<code class="card-code">${value}</code>` : '-'
                },
                {
                    key: 'name',
                    title: '姓名',
                    render: (value) => value || '-'
                },
                {
                    key: 'idcard',
                    title: '身份证',
                    render: (value) => value ? `<code class="id-card">${value}</code>` : '-'
                },
                {
                    key: 'type',
                    title: '类型',
                    render: (value) => `<span class="query-type">${value}</span>`
                },
                {
                    key: 'remainingUses',
                    title: '剩余次数',
                    render: (value) => {
                        if (value === null || value === undefined) return '-';
                        const color = value > 0 ? 'success' : 'warning';
                        return `<span class="text-${color}">${value}</span>`;
                    }
                },
                {
                    key: 'actions',
                    title: '操作',
                    render: (value, row) => `
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info view-demo-result" data-result='${JSON.stringify(row.result)}'>
                                <i class="fas fa-eye"></i>
                                查看结果
                            </button>
                        </div>
                    `
                }
            ];
        }
    }

    setLogType(type) {
        this.currentLogType = type;
        
        // Update active tab
        utils.$$('.log-type-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        utils.$(`[data-type="${type}"]`).classList.add('active');
        
        // Update table columns and title
        this.dataTable.options.columns = this.getTableColumns();
        this.dataTable.options.title = type === 'api' ? 'API日志列表' : 'Demo日志列表';
        this.dataTable.render();
        
        // Load logs for the new type
        this.loadLogs();
    }

    async loadLogs() {
        try {
            loading.show('加载日志数据...');

            if (this.currentLogType === 'api') {
                const response = await api.getLogStats(this.currentDate, 'api');
                this.logs = response.logs || [];
            } else {
                const response = await api.getLogStats(this.currentDate, 'demo');
                this.demoLogs = response.logs || [];
            }

            loading.hide();

            this.updateStats();
            this.applyFilters();

            toast.success('日志数据加载成功');
        } catch (error) {
            loading.hide();
            console.error('加载日志数据失败:', error);
            toast.error('加载日志数据失败: ' + error.message);
        }
    }

    updateStats() {
        const currentLogs = this.currentLogType === 'api' ? this.logs : this.demoLogs;
        
        const totalLogs = currentLogs.length;
        const uniqueUsers = new Set(currentLogs.map(log => 
            this.currentLogType === 'api' ? log.token : log.cardKey
        ).filter(Boolean)).size;
        
        const latestLog = currentLogs.length > 0 ? currentLogs[0] : null;
        const latestTime = latestLog ? utils.formatRelativeTime(latestLog.timestamp) : '-';
        
        const apiCalls = this.currentLogType === 'api' ? 
            new Set(currentLogs.map(log => log.api).filter(Boolean)).size : 
            currentLogs.length;

        utils.$('#total-logs-count').textContent = utils.formatNumber(totalLogs);
        utils.$('#unique-users-count').textContent = utils.formatNumber(uniqueUsers);
        utils.$('#latest-log-time').textContent = latestTime;
        utils.$('#api-calls-count').textContent = utils.formatNumber(apiCalls);
    }

    applyFilters() {
        const currentLogs = this.currentLogType === 'api' ? this.logs : this.demoLogs;
        let filteredLogs = [...currentLogs];
        
        // User filter
        const userFilter = utils.$('#user-filter').value.trim().toLowerCase();
        if (userFilter) {
            filteredLogs = filteredLogs.filter(log => {
                const identifier = this.currentLogType === 'api' ? log.token : log.cardKey;
                return identifier && identifier.toLowerCase().includes(userFilter);
            });
        }
        
        // API filter (only for API logs)
        if (this.currentLogType === 'api') {
            const apiFilter = utils.$('#api-filter').value;
            if (apiFilter) {
                filteredLogs = filteredLogs.filter(log => 
                    log.api && log.api.includes(apiFilter)
                );
            }
        }
        
        this.dataTable.setData(filteredLogs);
        
        // Bind table events
        this.bindTableEvents();
    }

    bindTableEvents() {
        const container = utils.$('#logs-table-container');
        
        // View parameters
        utils.delegate(container, '.view-params', 'click', (e) => {
            const params = JSON.parse(e.target.dataset.params);
            this.showParamsModal(params);
        });
        
        // Copy log
        utils.delegate(container, '.copy-log', 'click', async (e) => {
            const log = JSON.parse(e.target.dataset.log);
            const logText = JSON.stringify(log, null, 2);
            const success = await utils.copyToClipboard(logText);
            if (success) {
                toast.success('日志已复制到剪贴板');
            } else {
                toast.error('复制失败');
            }
        });
        
        // View demo result
        utils.delegate(container, '.view-demo-result', 'click', (e) => {
            const result = JSON.parse(e.target.dataset.result);
            this.showDemoResultModal(result);
        });
    }

    showParamsModal(params) {
        const paramsList = Object.entries(params).map(([key, value]) => `
            <div class="param-item">
                <strong>${key}:</strong>
                <span>${typeof value === 'object' ? JSON.stringify(value) : value}</span>
            </div>
        `).join('');

        modal.show({
            title: '请求参数',
            content: `<div class="params-list">${paramsList}</div>`,
            buttons: [
                {
                    text: '关闭',
                    className: 'btn-primary'
                }
            ]
        });
    }

    showDemoResultModal(result) {
        if (!result || typeof result !== 'object') {
            modal.alert('无效的结果数据');
            return;
        }

        const resultContent = `
            <div class="demo-result">
                <div class="result-item">
                    <strong>状态码:</strong>
                    <span class="status-code">${result.code}</span>
                </div>
                <div class="result-item">
                    <strong>消息:</strong>
                    <span>${result.message}</span>
                </div>
                ${result.data ? `
                    <div class="result-item">
                        <strong>数据:</strong>
                        <pre class="result-data">${JSON.stringify(result.data, null, 2)}</pre>
                    </div>
                ` : ''}
            </div>
        `;

        modal.show({
            title: 'Demo查询结果',
            content: resultContent,
            size: 'large',
            buttons: [
                {
                    text: '关闭',
                    className: 'btn-primary'
                }
            ]
        });
    }

    clearFilters() {
        utils.$('#user-filter').value = '';
        utils.$('#api-filter').value = '';
        this.applyFilters();
        toast.info('筛选条件已清除');
    }

    exportLogs() {
        const currentLogs = this.currentLogType === 'api' ? this.logs : this.demoLogs;
        
        if (currentLogs.length === 0) {
            toast.warning('没有日志数据可导出');
            return;
        }

        let csvContent;
        
        if (this.currentLogType === 'api') {
            const headers = ['时间', '用户Token', 'API', 'IP地址', '参数'];
            csvContent = [
                headers.join(','),
                ...currentLogs.map(log => [
                    log.timestamp,
                    log.token || '',
                    log.api || '',
                    Array.isArray(log.ips) ? log.ips.join(';') : '',
                    JSON.stringify(log.params || {})
                ].join(','))
            ].join('\n');
        } else {
            const headers = ['时间', '卡密', '姓名', '身份证', '类型', '剩余次数'];
            csvContent = [
                headers.join(','),
                ...currentLogs.map(log => [
                    log.timestamp,
                    log.cardKey || '',
                    log.name || '',
                    log.idcard || '',
                    log.type || '',
                    log.remainingUses || ''
                ].join(','))
            ].join('\n');
        }

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.currentLogType}_logs_${this.currentDate}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        toast.success('日志数据已导出');
    }

    async refreshLogs() {
        const refreshBtn = utils.$('#refresh-logs-btn');
        if (refreshBtn) {
            refreshBtn.classList.add('spinning');
        }

        try {
            await this.loadLogs();
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('spinning');
            }
        }
    }

    refresh() {
        this.loadLogs();
    }
}

// Export for global access
window.LogsPage = LogsPage;
